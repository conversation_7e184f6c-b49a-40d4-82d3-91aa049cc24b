package mcp

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	mcprouter "github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	mcpService "github.com/flipped-aurora/gin-vue-admin/server/service/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/jsonrpc"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpclient"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpserver"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mq"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type ProjectsApi struct{}

const DefaultLogoURL = "@https://c.mcpcn.cc/file/default/logo_light.png"

// parseJSONRPCID 统一处理 JSON-RPC 响应中的 ID 字段类型转换
func parseJSONRPCID(idVal interface{}) int {
	switch v := idVal.(type) {
	case float64:
		return int(v)
	case int:
		return v
	case string:
		if v == "0" {
			return 0
		} else if v == "1" {
			return 1
		} else if v == "2" {
			return 2
		}
	}
	return -1 // 返回 -1 表示无效 ID
}

// CreateProjects 创建projects表
// @Tags Projects
// @Summary 创建projects表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.Projects true "创建projects表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projects/createProjects [post]
func (projectsApi *ProjectsApi) CreateProjects(c *gin.Context) {
	var projects mcp.Projects
	err := c.ShouldBindJSON(&projects)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if projects.LogoUrl == "" {
		projects.LogoUrl = DefaultLogoURL
	}

	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if projects.Questions == "" {
		projects.Questions = "[]"
	}
	if projects.Args == "" {
		projects.Args = "[]"
	}
	if projects.Env == "" {
		projects.Env = "{}"
	}
	if projects.EnvReal == "" {
		projects.EnvReal = "{}"
	}
	if projects.Headers == "" {
		projects.Headers = "{}"
	}
	// 新增：必填变量默认值
	if projects.RequiredVariables == "" {
		projects.RequiredVariables = "[]"
	}
	if len(projects.SupportedPlatforms) == 0 {
		projects.SupportedPlatforms = datatypes.JSON("[]")
	}
	if projects.RequiredVariables == "" {
		projects.RequiredVariables = "[]"
	}

	if projects.Args != "" && projects.Args != "[]" {
		projects.Args = strings.ToLower(projects.Args)
	}

	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 确保代理字段被保存到数据库
	global.GVA_LOG.Info("创建项目，代理字段", zap.String("proxySseUrl", projects.ProxySseUrl), zap.String("proxyHttpUrl", projects.ProxyHttpUrl))

	err = tx.Create(&projects).Error
	if err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}

	callMethods := strings.Split(projects.CallMethod, ",")
	hasOnline := false
	hasLocal := false
	for _, m := range callMethods {
		m = strings.TrimSpace(m)
		if m == "online" {
			hasOnline = true
		}
		if m == "local" {
			hasLocal = true
		}
	}
	if hasOnline {
		// 当 callMethod 是 "online" 时，支持第三方代理
		if strings.Contains(projects.CallMethod, "online") && (projects.ProxySseUrl != "" || projects.ProxyHttpUrl != "") {
			// 使用第三方代理，command 非必填
			if err := saveOrUpdateServerKeyWithProxyTx(&projects, tx, projects.ProxySseUrl, projects.ProxyHttpUrl); err != nil {
				tx.Rollback()
				response.FailWithMessage("保存server_keys失败:"+err.Error(), c)
				return
			}
		} else {
			// 使用传统方式
			if err := saveOrUpdateServerKeyTx(&projects, tx); err != nil {
				tx.Rollback()
				response.FailWithMessage("保存server_keys失败:"+err.Error(), c)
				return
			}
		}
	}
	if hasLocal || hasOnline {
		global.GVA_LOG.Info("[Create] 开始获取工具列表", zap.String("project", projects.UUID))
		tools, err := FetchToolsListTx(tx, &projects)
		if err != nil {
			tx.Rollback()
			global.GVA_LOG.Error("[Create] 获取工具列表失败", zap.Error(err))
			response.FailWithMessage("获取工具列表失败:"+err.Error(), c)
			return
		}
		global.GVA_LOG.Info("[Create] 获取到工具数量", zap.Int("count", len(tools)))
		if err := InsertToolsForProjectTx(tx, &projects, tools); err != nil {
			tx.Rollback()
			global.GVA_LOG.Error("[Create] 插入工具失败", zap.Error(err))
			response.FailWithMessage("插入工具失败:"+err.Error(), c)
			return
		}
		global.GVA_LOG.Info("[Create] 插入工具成功", zap.Int("count", len(tools)))
	}
	tx.Commit()

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(c.Request.Context())

	response.OkWithMessage("创建成功", c)
}

// CreateProjectsSimple 简化创建projects表，不需要登录，直接包含tools
// @Tags Projects
// @Summary 简化创建projects表
// @Accept application/json
// @Produce application/json
// @Param data body request.ProjectsSimpleCreateRequest true "简化创建projects表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projects/createProjectsSimple [post]
func (projectsApi *ProjectsApi) CreateProjectsSimple(c *gin.Context) {
	var req request.ProjectsSimpleCreateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	projects := req.Projects
	if projects.LogoUrl == "" {
		projects.LogoUrl = DefaultLogoURL
	}

	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if projects.Questions == "" {
		projects.Questions = "[]"
	}
	if projects.Args == "" {
		projects.Args = "[]"
	}
	if projects.Env == "" {
		projects.Env = "{}"
	}
	if projects.EnvReal == "" {
		projects.EnvReal = "{}"
	}
	if projects.Headers == "" {
		projects.Headers = "{}"
	}
	if len(projects.SupportedPlatforms) == 0 {
		projects.SupportedPlatforms = datatypes.JSON("[]")
	}
	if projects.RequiredVariables == "" {
		projects.RequiredVariables = "[]"
	}

	if projects.Args != "" && projects.Args != "[]" {
		projects.Args = strings.ToLower(projects.Args)
	}

	// 使用带超时的事务，防止长时间锁定
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
	defer cancel()

	tx := global.GVA_DB.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	global.GVA_LOG.Info("简化创建项目", zap.String("name", projects.Name), zap.String("uuid", projects.UUID))

	// 验证UUID是否存在，如果存在则更新现有项目（保持ID不变，这样InsertToolsForProjectTx能找到现有工具）
	var existingProject mcp.Projects
	var isUpdate bool
	if projects.UUID != "" {
		// 使用 Unscoped() 来查找包括软删除的记录
		err := tx.Unscoped().Where("uuid = ?", projects.UUID).First(&existingProject).Error
		if err == nil {
			// UUID存在，更新现有项目而不是删除重建
			isUpdate = true
			global.GVA_LOG.Info("[CreateSimple] UUID已存在，更新现有项目", zap.String("uuid", projects.UUID), zap.Uint("id", existingProject.ID))

			// 保留原有的ID和时间戳，更新其他字段
			projects.ID = existingProject.ID
			projects.CreatedAt = existingProject.CreatedAt
			// 如果原记录是软删除的，需要恢复
			projects.DeletedAt = gorm.DeletedAt{}
		} else if !strings.Contains(err.Error(), "record not found") {
			// 其他数据库错误
			tx.Rollback()
			global.GVA_LOG.Error("[CreateSimple] 查询现有项目失败", zap.Error(err))
			response.FailWithMessage("查询现有项目失败:"+err.Error(), c)
			return
		}
		// 如果是 record not found 错误，说明UUID不存在，继续创建新项目
	}

	if isUpdate {
		// 更新现有项目，使用 Updates 而不是 Save 来避免更新所有字段
		updateData := map[string]interface{}{
			"name":                projects.Name,
			"description":         projects.Description,
			"llm_remark":          projects.LlmRemark,
			"logo_url":            projects.LogoUrl,
			"github":              projects.Github,
			"provider_url":        projects.ProviderUrl,
			"detail":              projects.Detail,
			"example_config":      projects.ExampleConfig,
			"call_method":         projects.CallMethod,
			"is_official":         projects.IsOfficial,
			"status":              projects.Status,
			"tags":                projects.Tags,
			"category":            projects.Category,
			"package":             projects.Package,
			"provider":            projects.Provider,
			"questions":           projects.Questions,
			"command":             projects.Command,
			"args":                projects.Args,
			"env":                 projects.Env,
			"env_real":            projects.EnvReal,
			"base_url":            projects.BaseUrl,
			"headers":             projects.Headers,
			"version":             projects.Version,
			"key_param_name":      projects.KeyParamName,
			"proxy_sse_url":       projects.ProxySseUrl,
			"proxy_http_url":      projects.ProxyHttpUrl,
			"required_variables":  projects.RequiredVariables,
			"supported_platforms": projects.SupportedPlatforms,
			"is_enabled":          false, // 设置为禁用状态
			"deleted_at":          nil,   // 恢复软删除的记录
		}
		err = tx.Model(&existingProject).Updates(updateData).Error
		if err != nil {
			tx.Rollback()
			// 检查是否是锁超时错误
			if strings.Contains(err.Error(), "Lock wait timeout exceeded") {
				global.GVA_LOG.Error("[CreateSimple] 数据库锁超时，请稍后重试", zap.Error(err))
				response.FailWithMessage("系统繁忙，请稍后重试", c)
			} else {
				global.GVA_LOG.Error("[CreateSimple] 更新现有项目失败", zap.Error(err))
				response.FailWithMessage("更新现有项目失败:"+err.Error(), c)
			}
			return
		}
		global.GVA_LOG.Info("[CreateSimple] 成功更新现有项目", zap.String("uuid", projects.UUID), zap.Uint("id", projects.ID))
	} else {
		// 创建新项目
		projects.IsEnabled = false // 设置为禁用状态
		err = tx.Create(&projects).Error
		if err != nil {
			tx.Rollback()
			// 检查是否是锁超时错误
			if strings.Contains(err.Error(), "Lock wait timeout exceeded") {
				global.GVA_LOG.Error("[CreateSimple] 数据库锁超时，请稍后重试", zap.Error(err))
				response.FailWithMessage("系统繁忙，请稍后重试", c)
			} else {
				global.GVA_LOG.Error("[CreateSimple] 创建新项目失败", zap.Error(err))
				response.FailWithMessage("创建失败:"+err.Error(), c)
			}
			return
		}
		global.GVA_LOG.Info("[CreateSimple] 成功创建新项目", zap.String("uuid", projects.UUID), zap.Uint("id", projects.ID))
	}

	if strings.Contains(projects.CallMethod, "online") && (projects.ProxySseUrl != "" || projects.ProxyHttpUrl != "") {

	} else {
		// 使用传统方式
		if err := saveOrUpdateServerKeyTx(&projects, tx); err != nil {
			tx.Rollback()
			response.FailWithMessage("保存server_keys失败:"+err.Error(), c)
			return
		}
	}

	// 直接插入传入的工具，不调用复杂的FetchToolsListTx
	if len(req.Tools) > 0 {
		global.GVA_LOG.Info("[CreateSimple] 开始插入工具", zap.String("project", projects.UUID), zap.Int("count", len(req.Tools)))
		// 将 []map[string]interface{} 转换为 []interface{}
		tools := make([]interface{}, len(req.Tools))
		for i, tool := range req.Tools {
			tools[i] = tool
		}
		if err := InsertToolsForProjectTx(tx, &projects, tools); err != nil {
			tx.Rollback()
			// 检查是否是锁超时错误
			if strings.Contains(err.Error(), "Lock wait timeout exceeded") {
				global.GVA_LOG.Error("[CreateSimple] 插入工具时数据库锁超时，请稍后重试", zap.Error(err))
				response.FailWithMessage("系统繁忙，请稍后重试", c)
			} else {
				global.GVA_LOG.Error("[CreateSimple] 插入工具失败", zap.Error(err))
				response.FailWithMessage("插入工具失败:"+err.Error(), c)
			}
			return
		}
		global.GVA_LOG.Info("[CreateSimple] 插入工具成功", zap.Int("count", len(req.Tools)))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		// 检查是否是锁超时错误
		if strings.Contains(err.Error(), "Lock wait timeout exceeded") {
			global.GVA_LOG.Error("[CreateSimple] 提交事务时数据库锁超时，请稍后重试", zap.Error(err))
			response.FailWithMessage("系统繁忙，请稍后重试", c)
		} else {
			global.GVA_LOG.Error("[CreateSimple] 提交事务失败", zap.Error(err))
			response.FailWithMessage("提交事务失败:"+err.Error(), c)
		}
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(c.Request.Context())

	response.OkWithMessage("创建成功", c)
}

// DeleteProjects 删除projects表
// @Tags Projects
// @Summary 删除projects表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.Projects true "删除projects表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projects/deleteProjects [delete]
func (projectsApi *ProjectsApi) DeleteProjects(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := projectsService.DeleteProjects(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(ctx)

	response.OkWithMessage("删除成功", c)
}

// DeleteProjectsByIds 批量删除projects表
// @Tags Projects
// @Summary 批量删除projects表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /projects/deleteProjectsByIds [delete]
func (projectsApi *ProjectsApi) DeleteProjectsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := projectsService.DeleteProjectsByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(ctx)

	response.OkWithMessage("批量删除成功", c)
}

// UpdateProjects 更新projects表
// @Tags Projects
// @Summary 更新projects表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.Projects true "更新projects表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projects/updateProjects [put]
func (projectsApi *ProjectsApi) UpdateProjects(c *gin.Context) {
	var projects mcp.Projects
	err := c.ShouldBindJSON(&projects)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if projects.Questions == "" {
		projects.Questions = "[]"
	}
	if projects.Args == "" {
		projects.Args = "[]"
	}
	if projects.Env == "" {
		projects.Env = "{}"
	}
	if projects.EnvReal == "" {
		projects.EnvReal = "{}"
	}
	if projects.Headers == "" {
		projects.Headers = "{}"
	}

	if projects.Args != "" && projects.Args != "[]" {
		projects.Args = strings.ToLower(projects.Args)
	}
	var oldProject mcp.Projects
	err = global.GVA_DB.First(&oldProject, projects.ID).Error
	if err != nil {
		response.FailWithMessage("未找到项目记录", c)
		return
	}
	projects.UUID = oldProject.UUID

	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 确保代理字段被更新到数据库
	global.GVA_LOG.Info("更新项目，代理字段",
		zap.String("proxySseUrl", projects.ProxySseUrl),
		zap.String("proxyHttpUrl", projects.ProxyHttpUrl),
		zap.String("callMethod", projects.CallMethod))

	// 用 map 明确指定所有需要更新的字段，布尔类型不会丢失
	updateMap := map[string]interface{}{
		"uuid":                projects.UUID,
		"name":                projects.Name,
		"description":         projects.Description,
		"llm_remark":          projects.LlmRemark,
		"logo_url":            projects.LogoUrl,
		"github":              projects.Github,
		"provider_url":        projects.ProviderUrl,
		"detail":              projects.Detail,
		"example_config":      projects.ExampleConfig,
		"call_method":         projects.CallMethod,
		"is_official":         projects.IsOfficial,
		"online_usage_count":  projects.OnlineUsageCount,
		"offline_usage_count": projects.OfflineUsageCount,
		"likes":               projects.Likes,
		"status":              projects.Status,
		"tags":                projects.Tags,
		"category":            projects.Category,
		"is_featured":         projects.IsFeatured,
		"sort":                projects.Sort,
		"allow_call":          projects.AllowCall,
		"is_enabled":          projects.IsEnabled,
		"package":             projects.Package,
		"provider":            projects.Provider,
		"path":                projects.Path,
		"questions":           projects.Questions,
		"command":             projects.Command,
		"args":                projects.Args,
		"env":                 projects.Env,
		"env_real":            projects.EnvReal,
		"base_url":            projects.BaseUrl,
		"headers":             projects.Headers,
		"version":             projects.Version,
		"key_param_name":      projects.KeyParamName,
		"proxy_sse_url":       projects.ProxySseUrl,
		"proxy_http_url":      projects.ProxyHttpUrl,
		"required_variables":  projects.RequiredVariables,
		"supported_platforms": projects.SupportedPlatforms,
	}

	// 调试：打印更新前的项目信息
	global.GVA_LOG.Info("更新前项目信息",
		zap.String("oldProxySseUrl", oldProject.ProxySseUrl),
		zap.String("oldProxyHttpUrl", oldProject.ProxyHttpUrl))

	// 调试：打印 updateMap 中的代理字段
	global.GVA_LOG.Info("updateMap 代理字段",
		zap.Any("proxy_sse_url", updateMap["proxy_sse_url"]),
		zap.Any("proxy_http_url", updateMap["proxy_http_url"]))

	err = tx.Model(&oldProject).Updates(updateMap).Error
	if err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}

	// 调试：打印更新后的项目信息
	var updatedProject mcp.Projects
	tx.First(&updatedProject, oldProject.ID)
	global.GVA_LOG.Info("更新后项目信息",
		zap.String("newProxySseUrl", updatedProject.ProxySseUrl),
		zap.String("newProxyHttpUrl", updatedProject.ProxyHttpUrl))

	callMethods := strings.Split(projects.CallMethod, ",")
	hasOnline := false
	hasLocal := false
	for _, m := range callMethods {
		m = strings.TrimSpace(m)
		if m == "online" {
			hasOnline = true
		}
		if m == "local" {
			hasLocal = true
		}
	}
	if hasOnline {
		// 使用更新后的项目数据
		updatedProject.ProxySseUrl = projects.ProxySseUrl
		updatedProject.ProxyHttpUrl = projects.ProxyHttpUrl

		// 当 callMethod 是 "online" 时，支持第三方代理
		if strings.Contains(projects.CallMethod, "online") && (projects.ProxySseUrl != "" || projects.ProxyHttpUrl != "") {
			// 使用第三方代理，command 非必填
			if err := saveOrUpdateServerKeyWithProxyTx(&updatedProject, tx, projects.ProxySseUrl, projects.ProxyHttpUrl); err != nil {
				tx.Rollback()
				response.FailWithMessage("保存server_keys失败:"+err.Error(), c)
				return
			}
		} else {
			// 使用传统方式
			if err := saveOrUpdateServerKeyTx(&updatedProject, tx); err != nil {
				tx.Rollback()
				response.FailWithMessage("保存server_keys失败:"+err.Error(), c)
				return
			}
		}
	}
	if hasLocal || hasOnline {
		//global.GVA_LOG.Info("[Update] 开始获取工具列表", zap.String("project", projects.UUID))
		//tools, err := FetchToolsListTx(tx, &projects)
		//if err != nil {
		//	tx.Rollback()
		//	global.GVA_LOG.Error("[Update] 获取工具列表失败", zap.Error(err))
		//	response.FailWithMessage("获取工具列表失败:"+err.Error(), c)
		//	return
		//}
		//global.GVA_LOG.Info("[Update] 获取到工具数量", zap.Int("count", len(tools)))
		//if err := InsertToolsForProjectTx(tx, &projects, tools); err != nil {
		//	tx.Rollback()
		//	global.GVA_LOG.Error("[Update] 插入工具失败", zap.Error(err))
		//	response.FailWithMessage("插入工具失败:"+err.Error(), c)
		//	return
		//}
		//global.GVA_LOG.Info("[Update] 插入工具成功", zap.Int("count", len(tools)))

		//TODO 待完成，需要调用向量检索端更新这些工具
	}
	tx.Commit()

	// 托管服务类型，通过Redis MQ发布项目更新消息，让所有服务器执行kill进程操作
	if strings.Contains(projects.CallMethod, "online") {
		if mq.GlobalRedisMQ != nil {
			err := mq.GlobalRedisMQ.PublishProjectUpdate(projects.UUID, projects.CallMethod, projects.Name)
			if err != nil {
				global.GVA_LOG.Error("发布项目更新消息失败",
					zap.String("projectUUID", projects.UUID),
					zap.String("projectName", projects.Name),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("成功发布项目更新消息",
					zap.String("projectUUID", projects.UUID),
					zap.String("projectName", projects.Name))
			}
		} else {
			global.GVA_LOG.Warn("Redis MQ服务未初始化，回退到本地kill进程",
				zap.String("projectUUID", projects.UUID))
			// 回退到原来的本地kill进程逻辑
			go func(serverKey string) {
				pids, err := utils.FindProcessPIDsByServerKey(serverKey)
				if err == nil {
					for _, pid := range pids {
						_ = utils.KillProcess(pid)
					}
				}
			}(projects.UUID)
		}
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(c.Request.Context())

	response.OkWithMessage("更新成功", c)
}

// FindProjects 用id查询projects表
// @Tags Projects
// @Summary 用id查询projects表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询projects表"
// @Success 200 {object} response.Response{data=mcp.Projects,msg=string} "查询成功"
// @Router /projects/findProjects [get]
func (projectsApi *ProjectsApi) FindProjects(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reprojects, err := projectsService.GetProjects(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}

	// 从 server_keys 表回显代理字段
	if err := populateProxyFieldsFromServerKeys(&reprojects); err != nil {
		global.GVA_LOG.Warn("回显代理字段失败", zap.Error(err))
	}

	// 调试：打印回显的代理字段
	global.GVA_LOG.Info("FindProjects 回显代理字段",
		zap.String("proxySseUrl", reprojects.ProxySseUrl),
		zap.String("proxyHttpUrl", reprojects.ProxyHttpUrl))

	response.OkWithData(reprojects, c)
}

// GetProjectsList
// @Tags      Projects
// @Summary   分页获取Projects列表
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.ProjectsSearch  true  "分页获取Projects列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "分页获取Projects列表,返回包括列表,总数,页码,每页数量"
// @Router    /projects/getProjectsList [post]
func (s *ProjectsApi) GetProjectsList(c *gin.Context) {
	ctx := c.Request.Context()
	var pageInfo request.ProjectsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := projectsService.GetProjectsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	// 清除敏感字段 envReal、proxySseUrl、proxyHttpUrl
	if projectsList, ok := list.([]mcp.Projects); ok {
		for i := range projectsList {
			projectsList[i].EnvReal = ""
			projectsList[i].ProxySseUrl = ""
			projectsList[i].ProxyHttpUrl = ""
		}
		list = projectsList
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProjectList 获取项目列表
// @Tags Projects
// @Summary 获取项目列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcpReq.ProjectsSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projects/getProjectList [post]
func (projectsApi *ProjectsApi) GetProjectList(c *gin.Context) {
	ctx := c.Request.Context()

	userUUID := utils.GetUserUuid(c)
	var search mcpReq.ProjectsSearch
	if err := c.ShouldBindJSON(&search); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if search.Page <= 0 {
		search.Page = 1
	}
	if search.PageSize <= 0 {
		search.PageSize = 10
	}

	list, total, err := projectsService.GetProjectsInfoList(ctx, search)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	// 类型断言，确保 list 是 []mcp.Projects
	projectsList, ok := list.([]mcp.Projects)
	if !ok {
		global.GVA_LOG.Error("返回的项目列表类型错误")
		response.FailWithMessage("返回的项目列表类型错误", c)
		return
	}

	// 遍历列表检查点赞状态

	if userUUID != uuid.Nil {
		for i := range projectsList {
			var isLiked bool
			isLiked, err = projectsService.CheckProjectLiked(c, projectsList[i].ID, userUUID)
			if err != nil {
				global.GVA_LOG.Error("检查项目点赞状态失败!", zap.Error(err))
				continue
			}
			projectsList[i].IsLiked = isLiked
		}
	}

	// 清除敏感字段 envReal、proxySseUrl、proxyHttpUrl
	for i := range projectsList {
		projectsList[i].EnvReal = ""
		projectsList[i].ProxySseUrl = ""
		projectsList[i].ProxyHttpUrl = ""
	}

	response.OkWithDetailed(response.PageResult{
		List:     projectsList,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
}

// FindProjectByUUID 根据UUID查找项目
func (p *ProjectsApi) FindProjectByUUID(c *gin.Context) {
	projectUUID := c.Param("uuid")
	userUUID := utils.GetUserUuid(c)
	var isLiked bool
	var err error

	project, err := projectsService.FindProjectByUUID(c, projectUUID)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	if userUUID != uuid.Nil {
		isLiked, err = projectsService.CheckProjectLiked(c, project.ID, userUUID)
		if err != nil {
			global.GVA_LOG.Error("检查项目点赞状态失败!", zap.Error(err))
		}
	}

	project.IsLiked = isLiked

	// 新增：将args、env、headers字段转为json对象或数组，保持原始类型，若为空则返回空对象或空数组
	var argsObj, envObj, headersObj interface{}

	if project.Args != "" {
		_ = json.Unmarshal([]byte(project.Args), &argsObj)
	}
	if argsObj == nil {
		argsObj = map[string]interface{}{}
		if project.Args != "" && project.Args[0] == '[' {
			argsObj = []interface{}{}
		}
	}

	if project.Env != "" && project.Env != "\"\"" {
		_ = json.Unmarshal([]byte(project.Env), &envObj)
	}
	if envObj == nil {
		envObj = map[string]interface{}{}
		if project.Env != "" && project.Env != "\"\"" && project.Env[0] == '[' {
			envObj = []interface{}{}
		}
	}

	if project.Headers != "" {
		_ = json.Unmarshal([]byte(project.Headers), &headersObj)
	}
	if headersObj == nil {
		headersObj = map[string]interface{}{}
		if project.Headers != "" && project.Headers[0] == '[' {
			headersObj = []interface{}{}
		}
	}

	// 清除敏感字段 envReal、proxySseUrl、proxyHttpUrl
	project.EnvReal = ""
	project.ProxySseUrl = ""
	project.ProxyHttpUrl = ""

	// 从 server_keys 表回显代理字段（仅用于内部逻辑，不返回给前端）
	if err := populateProxyFieldsFromServerKeys(project); err != nil {
		global.GVA_LOG.Warn("回显代理字段失败", zap.Error(err))
	}

	// 构造返回对象
	resp := map[string]interface{}{}
	b, _ := json.Marshal(project)
	_ = json.Unmarshal(b, &resp)
	resp["args"] = argsObj
	resp["env"] = envObj
	resp["headers"] = headersObj

	// 获取用户apikey
	userId := utils.GetUserID(c)
	var apiKey string
	if userId != 0 {
		if keyObj, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.FindLatestApiKey(userId); err == nil && keyObj != nil {
			apiKey = keyObj.ApiKey
		}
	}

	// 处理sseUrl和streamableHttpUrl
	sseUrl := ""
	streamableHttpUrl := ""
	if project.BaseUrl != "" {
		if apiKey != "" {
			sseUrl = project.BaseUrl + "/" + apiKey
			streamableHttpUrl = project.BaseUrl
			if strings.Contains(streamableHttpUrl, "/sse/") {
				streamableHttpUrl = strings.Replace(streamableHttpUrl, "/sse/", "/mcp/", 1)
			}
			streamableHttpUrl = streamableHttpUrl + "/" + apiKey
		} else {
			sseUrl = project.BaseUrl
			streamableHttpUrl = project.BaseUrl
			if strings.Contains(streamableHttpUrl, "/sse/") {
				streamableHttpUrl = strings.Replace(streamableHttpUrl, "/sse/", "/mcp/", 1)
			}
		}
	}

	// 查询 servers_key
	serverKey, err := mcprouter.FindServerkeyByServerUUID(project.UUID)
	// 新增逻辑：根据 BaseUrl 查询 servers_key，判断 server_command
	serverCommandNotEmpty := false
	{
		// 引入 mcprouter 包
		importMcprouter := false
		_ = importMcprouter // 避免未使用
		if err == nil && serverKey != nil && serverKey.ServerCommand != "" {
			serverCommandNotEmpty = true
		}
	}

	callType := 0
	if serverCommandNotEmpty {
		resp["sseUrl"] = sseUrl
		resp["streamableHttpUrl"] = streamableHttpUrl
		if sseUrl != "" && streamableHttpUrl != "" {
			callType = 3
		} else if sseUrl != "" {
			callType = 1
		} else if streamableHttpUrl != "" {
			callType = 2
		}
	} else {
		if err == nil && serverKey != nil && serverKey.SseUrl != "" {
			resp["sseUrl"] = sseUrl
			callType += 1
		} else {
			resp["sseUrl"] = ""
		}
		if err == nil && serverKey != nil && serverKey.StreamableHttpUrl != "" {
			resp["streamableHttpUrl"] = streamableHttpUrl
			callType += 2
		} else {
			resp["streamableHttpUrl"] = ""
		}
	}
	resp["callType"] = callType

	// 显式处理 ProjectTools，确保包含 regex 和 c_name 字段
	if tools, ok := resp["projectTools"].([]interface{}); ok {
		for i, t := range project.ProjectTools {
			if i < len(tools) {
				if toolMap, ok := tools[i].(map[string]interface{}); ok {
					toolMap["regex"] = t.Regex
					toolMap["c_name"] = t.CName
				}
			}
		}
	}

	response.OkWithData(resp, c)
}

// CreateProjectWithUUID 创建带UUID的项目
// @Tags Projects
// @Summary 创建带UUID的项目
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.Projects true "创建项目"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projects/createProjectWithUUID [post]
func (projectsApi *ProjectsApi) CreateProjectWithUUID(c *gin.Context) {
	ctx := c.Request.Context()

	var project mcp.Projects
	if err := c.ShouldBindJSON(&project); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 如果 logo_url 为空，赋默认值
	if project.LogoUrl == "" {
		project.LogoUrl = DefaultLogoURL
	}

	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if project.Questions == "" {
		project.Questions = "[]"
	}
	if project.Args == "" {
		project.Args = "[]"
	}
	if project.Env == "" {
		project.Env = "{}"
	}
	if project.EnvReal == "" {
		project.EnvReal = "{}"
	}
	if project.Headers == "" {
		project.Headers = "{}"
	}
	if len(project.SupportedPlatforms) == 0 {
		project.SupportedPlatforms = datatypes.JSON("[]")
	}
	if project.RequiredVariables == "" {
		project.RequiredVariables = "[]"
	}
	if len(project.SupportedPlatforms) == 0 {
		project.SupportedPlatforms = datatypes.JSON("[]")
	}
	if project.RequiredVariables == "" {
		project.RequiredVariables = "[]"
	}

	err := projectsService.CreateProjects(ctx, &project)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(ctx)

	response.OkWithMessage("创建成功", c)
}

// UpdateProjectByUUID 更新项目
// @Tags Projects
// @Summary 更新项目
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mcp.Projects true "更新项目"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projects/updateProjectByUUID [put]
func (projectsApi *ProjectsApi) UpdateProjectByUUID(c *gin.Context) {
	ctx := c.Request.Context()

	var project mcp.Projects
	if err := c.ShouldBindJSON(&project); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if project.Questions == "" {
		project.Questions = "[]"
	}
	if project.Args == "" {
		project.Args = "[]"
	}
	if project.Env == "" {
		project.Env = "{}"
	}
	if project.EnvReal == "" {
		project.EnvReal = "{}"
	}
	if project.Headers == "" {
		project.Headers = "{}"
	}

	err := projectsService.UpdateProjects(ctx, project)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(ctx)

	response.OkWithMessage("更新成功", c)
}

// DeleteProjectByUUID 删除项目
// @Tags Projects
// @Summary 删除项目
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param uuid path string true "项目UUID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projects/deleteProjectByUUID/{uuid} [delete]
func (projectsApi *ProjectsApi) DeleteProjectByUUID(c *gin.Context) {
	ctx := c.Request.Context()

	uuid := c.Param("uuid")
	err := projectsService.DeleteProjects(ctx, uuid)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}

	// 清除工具库查询缓存
	clearProjectsSearchCacheInternal(ctx)

	response.OkWithMessage("删除成功", c)
}

// IncrementOnlineUsageCount 增加在线使用次数
// @Tags Projects
// @Summary 增加在线使用次数
// @Accept application/json
// @Produce application/json
// @Param projectID path string true "项目ID"
// @Success 200 {object} response.Response{msg=string} "操作成功"
// @Router /projects/incrementOnlineUsageCount/{projectID} [post]
func (projectsApi *ProjectsApi) IncrementOnlineUsageCount(c *gin.Context) {
	ctx := c.Request.Context()
	projectID := c.Param("projectID")

	err := projectsService.IncrementOnlineUsageCount(ctx, projectID)
	if err != nil {
		global.GVA_LOG.Error("增加在线使用次数失败!", zap.Error(err))
		response.FailWithMessage("操作失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("操作成功", c)
}

// IncrementOfflineUsageCount 增加离线使用次数
// @Tags Projects
// @Summary 增加离线使用次数
// @Accept application/json
// @Produce application/json
// @Param projectID path string true "项目ID"
// @Success 200 {object} response.Response{msg=string} "操作成功"
// @Router /projects/incrementOfflineUsageCount/{projectID} [post]
func (projectsApi *ProjectsApi) IncrementOfflineUsageCount(c *gin.Context) {
	ctx := c.Request.Context()
	projectID := c.Param("projectID")

	err := projectsService.IncrementOfflineUsageCount(ctx, projectID)
	if err != nil {
		global.GVA_LOG.Error("增加离线使用次数失败!", zap.Error(err))
		response.FailWithMessage("操作失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("操作成功", c)
}

// LikeProject 点赞项目
func (p *ProjectsApi) LikeProject(c *gin.Context) {
	projectID := c.Param("projectID")
	projectIDUint, err := strconv.ParseUint(projectID, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的项目ID", c)
		return
	}
	userUUID := utils.GetUserUuid(c)
	if userUUID == uuid.Nil {
		response.FailWithMessage("用户未登录", c)
		return
	}
	err = projectsService.LikeProject(c, uint(projectIDUint), userUUID)
	if err != nil {
		global.GVA_LOG.Error("点赞失败!", zap.Error(err))
		response.FailWithMessage("点赞失败", c)
		return
	}
	response.OkWithMessage("点赞成功", c)
}

// UnlikeProject 取消点赞项目
func (p *ProjectsApi) UnlikeProject(c *gin.Context) {
	projectID := c.Param("projectID")
	projectIDUint, err := strconv.ParseUint(projectID, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的项目ID", c)
		return
	}
	userUUID := utils.GetUserUuid(c)
	if userUUID == uuid.Nil {
		response.FailWithMessage("用户未登录", c)
		return
	}
	err = projectsService.UnlikeProject(c, uint(projectIDUint), userUUID)
	if err != nil {
		global.GVA_LOG.Error("取消点赞失败!", zap.Error(err))
		response.FailWithMessage("取消点赞失败", c)
		return
	}
	response.OkWithMessage("取消点赞成功", c)
}

// GetAdminProjectsList 管理端获取项目列表
// @Tags Projects
// @Summary 管理端获取项目列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.ProjectsSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projects/getProjectsList [get]
func (s *ProjectsApi) GetAdminProjectsList(c *gin.Context) {
	ctx := c.Request.Context()
	var search request.ProjectsSearch
	if err := c.ShouldBindQuery(&search); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if search.Page <= 0 {
		search.Page = 1
	}
	if search.PageSize <= 0 {
		search.PageSize = 10
	}

	list, total, err := projectsService.GetAdminProjectsInfoList(ctx, search)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	// 清除敏感字段 envReal、proxySseUrl、proxyHttpUrl
	if projectsList, ok := list.([]mcp.Projects); ok {
		for i := range projectsList {
			projectsList[i].EnvReal = ""
			projectsList[i].ProxySseUrl = ""
			projectsList[i].ProxyHttpUrl = ""
		}
		list = projectsList
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
}

// SubmitMcp 提交mcp项目
// @Tags Projects
// @Summary 提交mcp项目
// @Accept application/json
// @Produce application/json
// @Param data body mcpReq.ProjectsSubmitRequest true "提交mcp项目"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /projects/submitMcp [post]
func (projectsApi *ProjectsApi) SubmitMcp(c *gin.Context) {
	ctx := c.Request.Context()
	var req mcpReq.ProjectsSubmitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 构造Projects对象
	project := mcp.Projects{
		Name:        req.Name,
		Description: req.Description,
		AuditStatus: "pending",
		Questions:   "[]",
	}
	if len(req.Questions) > 0 {
		questionsJson, _ := json.Marshal(req.Questions)
		project.Questions = string(questionsJson)
	}

	// 对 JSON 字段进行预处理，防止空字符串导致 MySQL 报错
	if project.Args == "" {
		project.Args = "[]"
	}
	if project.Env == "" {
		project.Env = "{}"
	}
	if project.EnvReal == "" {
		project.EnvReal = "{}"
	}
	if project.Headers == "" {
		project.Headers = "{}"
	}
	if len(project.SupportedPlatforms) == 0 {
		project.SupportedPlatforms = datatypes.JSON("[]")
	}
	if project.RequiredVariables == "" {
		project.RequiredVariables = "[]"
	}

	err := projectsService.CreateProjects(ctx, &project)
	if err != nil {
		global.GVA_LOG.Error("项目提交失败!", zap.Error(err))
		response.FailWithMessage("项目提交失败:"+err.Error(), c)
		return
	}

	// 插入ProjectTools
	for _, tool := range req.Tools {
		// 设置新字段的默认值
		supportedExtensions := datatypes.JSON("[]")
		platforms := datatypes.JSON("[]")
		multiFileType := 0
		canHandleDirectory := 0
		canDirectExecute := 0
		isDangerous := 0
		isDisabled := 0

		pt := mcp.ProjectTools{
			SupportedExtensions: supportedExtensions,
			MultiFileType:       &multiFileType,
			Keywords:            nil,
			Platforms:           platforms,
			CanHandleDirectory:  &canHandleDirectory,
			PrerequisiteToolId:  nil,
			CanDirectExecute:    &canDirectExecute,
			IsDangerous:         &isDangerous,
			IsDisabled:          &isDisabled,
		}
		if project.ID != 0 {
			pid := int(project.ID)
			pt.ProjectId = &pid
		}
		if name, ok := tool["name"].(string); ok {
			pt.Name = &name
		}
		if desc, ok := tool["description"].(string); ok {
			d := desc
			pt.Description = &d
		}
		// 可扩展更多字段
		_ = projectToolsService.CreateProjectTools(ctx, &pt)
	}

	response.OkWithMessage("提交成功", c)
}

// GetMyProjects 获取我的项目列表
// @Tags Projects
// @Summary 获取我的项目列表
// @Accept application/json
// @Produce application/json
// @Param userUuid query string true "用户UUID"
// @Success 200 {object} response.Response{data=[]mcp.Projects,msg=string} "获取成功"
// @Router /projects/myProjects [get]
func (projectsApi *ProjectsApi) GetMyProjects(c *gin.Context) {
	ctx := c.Request.Context()
	userUuid := c.Query("userUuid")
	if userUuid == "" {
		response.FailWithMessage("userUuid不能为空", c)
		return
	}
	var search mcpReq.ProjectsSearch
	list, _, err := projectsService.GetProjectsInfoList(ctx, search)
	if err != nil {
		global.GVA_LOG.Error("获取我的项目失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	// 清除敏感字段 envReal、proxySseUrl、proxyHttpUrl
	if projectsList, ok := list.([]mcp.Projects); ok {
		for i := range projectsList {
			projectsList[i].EnvReal = ""
			projectsList[i].ProxySseUrl = ""
			projectsList[i].ProxyHttpUrl = ""
		}
		list = projectsList
	}

	response.OkWithData(list, c)
}

// 新增：支持事务的serverkey查找
func FindServerkeyByServerUUIDTx(tx *gorm.DB, uuid string) (*mcprouter.Serverkey, error) {
	var serverkey mcprouter.Serverkey
	err := tx.Where("server_uuid = ?", uuid).First(&serverkey).Error
	if err != nil {
		return nil, err
	}
	return &serverkey, nil
}

// 修改 saveOrUpdateServerKeyTx，支持第三方代理
func saveOrUpdateServerKeyTx(projects *mcp.Projects, tx *gorm.DB) error {
	return saveOrUpdateServerKeyWithProxyTx(projects, tx, "", "")
}

// 新增支持第三方代理的方法
func saveOrUpdateServerKeyWithProxyTx(projects *mcp.Projects, tx *gorm.DB, sseUrl, streamableHttpUrl string) error {
	var argsArr []string
	if projects.Args != "" && projects.Args != "\"\"" {
		if err := json.Unmarshal([]byte(projects.Args), &argsArr); err != nil {
			argsArr = []string{}
		}
	}
	serverCommand := projects.Command

	// 新增 npx 逻辑
	if projects.Command == "npx" {
		// 执行 shell 脚本，获取 node 路径
		packageName := projects.Package
		if packageName != "" {
			cmd := exec.Command("sh", global.GVA_CONFIG.MCP.DownloadScriptPath, packageName)
			output, err := cmd.Output()
			if err != nil {
				return fmt.Errorf("download.sh 执行失败: %v", err)
			}
			lines := strings.Split(strings.TrimSpace(string(output)), "\n")
			path := lines[len(lines)-1]
			if path == "" {
				return fmt.Errorf("download.sh 未返回有效路径")
			}
			serverCommand = "node " + path
		}
	} else {
		if len(argsArr) > 0 {
			serverCommand = serverCommand + " " + strings.Join(argsArr, " ")
		}
	}

	envJson := ""
	if projects.EnvReal != "" && projects.EnvReal != "\"\"" {
		envJson = projects.EnvReal
	}
	keyParamName := projects.KeyParamName

	// 当 callMethod 包含 "online" 且提供了第三方代理 URL 时，command 可以为空
	if strings.Contains(projects.CallMethod, "online") && (sseUrl != "" || streamableHttpUrl != "") {
		serverCommand = "" // 第三方代理时不需要本地命令
	}

	serverKey, err := FindServerkeyByServerUUIDTx(tx, projects.UUID)
	if err == nil && serverKey.ID != 0 {
		updateData := map[string]interface{}{
			"ServerKey":         projects.UUID,
			"ServerUUID":        projects.UUID,
			"ServerName":        projects.Name,
			"ServerCommand":     serverCommand,
			"EnvJson":           envJson,
			"KeyParamName":      keyParamName,
			"Status":            "created",
			"UserUUID":          "system",
			"SseUrl":            sseUrl,
			"StreamableHttpUrl": streamableHttpUrl,
		}
		return tx.Model(&serverKey).Updates(updateData).Error
	} else if err != nil && strings.Contains(err.Error(), "record not found") {
		newKey := mcprouter.Serverkey{
			ServerKey:         projects.UUID,
			ServerUUID:        projects.UUID,
			ServerName:        projects.Name,
			ServerCommand:     serverCommand,
			KeyParamName:      keyParamName,
			Status:            "created",
			EnvJson:           envJson,
			UserUUID:          "system",
			CreatedAt:         time.Now(),
			SseUrl:            sseUrl,
			StreamableHttpUrl: streamableHttpUrl,
		}
		return tx.Create(&newKey).Error
	} else if err == nil {
		return tx.Save(serverKey).Error
	} else {
		return err
	}
}

// populateProxyFieldsFromServerKeys 从 server_keys 表回显代理字段到 projects
func populateProxyFieldsFromServerKeys(project *mcp.Projects) error {
	if project == nil || project.UUID == "" {
		return nil
	}

	serverkey, err := mcprouter.FindServerkeyByServerUUID(project.UUID)
	if err != nil {
		// 如果没有找到 serverkey，不算错误，直接返回
		if strings.Contains(err.Error(), "record not found") {
			global.GVA_LOG.Info("populateProxyFieldsFromServerKeys: 未找到 serverkey", zap.String("uuid", project.UUID))
			return nil
		}
		return err
	}

	// 调试：打印从 server_keys 表读取的值
	global.GVA_LOG.Info("populateProxyFieldsFromServerKeys: 从 server_keys 读取",
		zap.String("uuid", project.UUID),
		zap.String("sseUrl", serverkey.SseUrl),
		zap.String("streamableHttpUrl", serverkey.StreamableHttpUrl))

	// 回显代理字段
	if serverkey.SseUrl != "" {
		project.ProxySseUrl = serverkey.SseUrl
	}
	if serverkey.StreamableHttpUrl != "" {
		project.ProxyHttpUrl = serverkey.StreamableHttpUrl
	}

	// 调试：打印回显后的项目字段
	global.GVA_LOG.Info("populateProxyFieldsFromServerKeys: 回显后",
		zap.String("proxySseUrl", project.ProxySseUrl),
		zap.String("proxyHttpUrl", project.ProxyHttpUrl))

	return nil
}

// 修改 fetchToolsList，事务内查找 serverkey
func FetchToolsListTx(tx *gorm.DB, project *mcp.Projects) ([]interface{}, error) {
	var tools []interface{}
	serverkey, err := FindServerkeyByServerUUIDTx(tx, project.UUID)

	// 调试：打印 serverkey 查找结果
	if err != nil {
		global.GVA_LOG.Info("[fetchToolsListTx] 未找到 serverkey", zap.String("uuid", project.UUID), zap.Error(err))
	} else if serverkey == nil {
		global.GVA_LOG.Info("[fetchToolsListTx] serverkey 为 nil", zap.String("uuid", project.UUID))
	} else {
		global.GVA_LOG.Info("[fetchToolsListTx] 找到 serverkey",
			zap.String("uuid", project.UUID),
			zap.String("sseUrl", serverkey.SseUrl),
			zap.String("streamableHttpUrl", serverkey.StreamableHttpUrl),
			zap.String("serverCommand", serverkey.ServerCommand))
	}
	if serverkey != nil && serverkey.SseUrl != "" {
		global.GVA_LOG.Info("[fetchToolsListTx] 使用SSE模式获取工具列表", zap.String("sseUrl", serverkey.SseUrl))
		headers := http.Header{}
		if serverkey.EnvJson != "" {
			var env map[string]interface{}
			_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
			for k, v := range env {
				headers.Set(k, fmt.Sprintf("%v", v))
			}
		}
		client := &http.Client{Timeout: 60 * time.Second}
		global.GVA_LOG.Info("[fetchToolsList] SSE请求", zap.String("url", serverkey.SseUrl), zap.Any("headers", headers))
		req, _ := http.NewRequest("GET", serverkey.SseUrl, nil)
		req.Header = headers
		resp, err := client.Do(req)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] SSE连接失败", zap.Error(err))
			return nil, err
		}
		defer resp.Body.Close()
		scanner := bufio.NewScanner(resp.Body)
		var dataBuf string
		var lastEvent string
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "event:") {
				lastEvent = strings.TrimSpace(strings.TrimPrefix(line, "event:"))
			} else if strings.HasPrefix(line, "data:") {
				data := strings.TrimSpace(strings.TrimPrefix(line, "data:"))
				if lastEvent == "endpoint" {
					dataBuf = data
					break
				}
			}
		}
		if dataBuf == "" {
			global.GVA_LOG.Error("[fetchToolsList] SSE未获取到endpoint")
			return nil, fmt.Errorf("SSE未获取到endpoint")
		}
		u, _ := url.Parse(serverkey.SseUrl)
		var messageUrl string
		if strings.HasPrefix(dataBuf, "http") {
			messageUrl = dataBuf
		} else if strings.HasPrefix(dataBuf, "/") {
			messageUrl = u.Scheme + "://" + u.Host + dataBuf
		} else {
			messageUrl = u.Scheme + "://" + u.Host + "/" + dataBuf
		}
		global.GVA_LOG.Info("[fetchToolsList] SSE messageUrl", zap.String("url", messageUrl))

		// JSON-RPC 请求 ID 计数器，从 0 开始
		var requestID int = 0

		// 准备 JSON-RPC 请求
		listReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"id":      requestID + 1, // tools/list 请求使用 ID 1
			"method":  jsonrpc.MethodListTools,
			"params":  map[string]interface{}{},
		}
		listBytes, _ := json.Marshal(listReq)
		listMsgReq, _ := http.NewRequest("POST", messageUrl, bytes.NewReader(listBytes))
		listMsgReq.Header = headers
		listMsgReq.Header.Set("Content-Type", "application/json")
		global.GVA_LOG.Info("[fetchToolsList] SSE listTools请求", zap.String("url", messageUrl), zap.ByteString("body", listBytes))

		// 启动 SSE 流消息监听，按 id 分发响应
		responseCh := make(chan map[string]interface{}, 2)
		errorCh := make(chan error, 1)
		ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second) // 3分钟总超时
		defer cancel()

		go func() {
			defer close(responseCh)
			scanner := bufio.NewScanner(resp.Body)
			for scanner.Scan() {
				select {
				case <-ctx.Done():
					errorCh <- fmt.Errorf("SSE流读取超时")
					return
				default:
				}

				line := scanner.Text()
				if strings.HasPrefix(line, "data:") {
					data := strings.TrimSpace(strings.TrimPrefix(line, "data:"))
					global.GVA_LOG.Info("[fetchToolsList] SSE data event", zap.String("data", data))
					var msg map[string]interface{}
					if err := json.Unmarshal([]byte(data), &msg); err == nil {
						if msg["id"] != nil {
							id := parseJSONRPCID(msg["id"])
							if id == 0 || id == 1 { // 0: initialize, 1: tools/list
								global.GVA_LOG.Info("[fetchToolsList] SSE推送JSON-RPC响应", zap.Any("msg", msg))
								select {
								case responseCh <- msg:
								case <-ctx.Done():
									errorCh <- fmt.Errorf("SSE流读取超时")
									return
								}
							}
						}
					}
				}
			}
			if err := scanner.Err(); err != nil {
				errorCh <- err
			}
		}()

		// 发送 initialize 请求
		initReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"id":      requestID, // initialize 请求使用 ID 0
			"method":  jsonrpc.MethodInitialize,
			"params": map[string]interface{}{
				"protocolVersion": jsonrpc.LATEST_PROTOCOL_VERSION,
				"clientInfo": map[string]interface{}{
					"name":    jsonrpc.PROXY_CLIENT_NAME,
					"version": jsonrpc.PROXY_CLIENT_VERSION,
				},
				"capabilities": map[string]interface{}{},
			},
		}
		initBytes, _ := json.Marshal(initReq)
		initMsgReq, _ := http.NewRequest("POST", messageUrl, bytes.NewReader(initBytes))
		initMsgReq.Header = headers
		initMsgReq.Header.Set("Content-Type", "application/json")
		_, err = client.Do(initMsgReq)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] 发送initialize请求失败", zap.Error(err))
			return nil, fmt.Errorf("发送initialize请求失败: %v", err)
		}

		// 在 SSE 流中查找 id=1 的响应，添加超时机制
		var initRespMsg map[string]interface{}
		initTimeout := time.NewTimer(90 * time.Second) // 90秒超时
		defer initTimeout.Stop()

	initLoop:
		for {
			select {
			case msg := <-responseCh:
				global.GVA_LOG.Info("[fetchToolsList] SSE流响应分发", zap.Any("msg", msg))
				if msg["id"] != nil && parseJSONRPCID(msg["id"]) == 0 { // initialize 使用 ID 0
					initRespMsg = msg
					break initLoop
				}
				// 如果不是 id=1 的响应，继续等待
			case <-initTimeout.C:
				global.GVA_LOG.Error("[fetchToolsList] 等待initialize响应超时")
				return nil, fmt.Errorf("等待initialize响应超时(90秒)")
			case err := <-errorCh:
				global.GVA_LOG.Error("[fetchToolsList] SSE流读取错误", zap.Error(err))
				return nil, fmt.Errorf("SSE流读取错误: %v", err)
			}
		}

		if initRespMsg == nil {
			global.GVA_LOG.Error("[fetchToolsList] 未收到initialize响应")
			return nil, fmt.Errorf("未收到initialize响应")
		}
		if errVal, ok := initRespMsg["error"]; ok && errVal != nil {
			global.GVA_LOG.Error("[fetchToolsList] initialize返回错误", zap.Any("error", errVal))
			return nil, fmt.Errorf("initialize返回错误: %v", errVal)
		}

		// 发送 notifications/initialized 通知
		notificationReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"method":  jsonrpc.MethodInitializedNotification,
			"params":  map[string]interface{}{},
		}
		notificationBytes, _ := json.Marshal(notificationReq)
		notificationMsgReq, _ := http.NewRequest("POST", messageUrl, bytes.NewReader(notificationBytes))
		notificationMsgReq.Header = headers
		notificationMsgReq.Header.Set("Content-Type", "application/json")
		_, err = client.Do(notificationMsgReq)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] 发送notifications/initialized失败", zap.Error(err))
			return nil, fmt.Errorf("发送notifications/initialized失败: %v", err)
		}
		global.GVA_LOG.Info("[fetchToolsList] 已发送notifications/initialized通知")

		// 发送 listTools 请求
		_, err = client.Do(listMsgReq)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] 发送listTools请求失败", zap.Error(err))
			return nil, fmt.Errorf("发送listTools请求失败: %v", err)
		}

		// 在 SSE 流中查找 id=2 的响应，添加超时机制
		var listRespMsg map[string]interface{}
		listTimeout := time.NewTimer(60 * time.Second) // 60秒超时
		defer listTimeout.Stop()

	listLoop:
		for {
			select {
			case msg := <-responseCh:
				global.GVA_LOG.Info("[fetchToolsList] SSE流响应分发", zap.Any("msg", msg))
				if msg["id"] != nil && parseJSONRPCID(msg["id"]) == 1 { // tools/list 使用 ID 1
					listRespMsg = msg
					break listLoop
				}
				// 如果不是 id=2 的响应，继续等待
			case <-listTimeout.C:
				global.GVA_LOG.Error("[fetchToolsList] 等待listTools响应超时")
				return nil, fmt.Errorf("等待listTools响应超时(60秒)")
			case err := <-errorCh:
				global.GVA_LOG.Error("[fetchToolsList] SSE流读取错误", zap.Error(err))
				return nil, fmt.Errorf("SSE流读取错误: %v", err)
			}
		}

		if listRespMsg == nil {
			global.GVA_LOG.Error("[fetchToolsList] 未收到listTools响应")
			return nil, fmt.Errorf("未收到listTools响应")
		}
		if errVal, ok := listRespMsg["error"]; ok && errVal != nil {
			global.GVA_LOG.Error("[fetchToolsList] listTools返回错误", zap.Any("error", errVal))
			return nil, fmt.Errorf("listTools返回错误: %v", errVal)
		}

		// 解析 tools 结果
		result, ok := listRespMsg["result"].(map[string]interface{})
		if !ok {
			global.GVA_LOG.Error("[fetchToolsList] listTools响应格式错误")
			return nil, fmt.Errorf("listTools响应格式错误")
		}
		tools, ok = result["tools"].([]interface{})
		if !ok {
			global.GVA_LOG.Error("[fetchToolsList] listTools响应缺少tools字段")
			return nil, fmt.Errorf("listTools响应缺少tools字段")
		}

		global.GVA_LOG.Info("[fetchToolsList] SSE成功获取工具列表", zap.Int("count", len(tools)))
		return tools, nil
	} else if serverkey != nil && serverkey.StreamableHttpUrl != "" {
		global.GVA_LOG.Info("[fetchToolsListTx] 使用HTTP模式获取工具列表", zap.String("streamableHttpUrl", serverkey.StreamableHttpUrl))
		headers := http.Header{}
		if serverkey.EnvJson != "" {
			var env map[string]interface{}
			_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
			for k, v := range env {
				headers.Set(k, fmt.Sprintf("%v", v))
			}
		}
		headers.Set("Accept", "application/json, text/event-stream")
		headers.Set("Content-Type", "application/json")

		client := &http.Client{Timeout: 30 * time.Second}

		// JSON-RPC 请求 ID 计数器，从 0 开始
		var requestID int = 0

		// 1. 发送 initialize 请求
		initReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"id":      requestID, // initialize 请求使用 ID 0
			"method":  jsonrpc.MethodInitialize,
			"params": map[string]interface{}{
				"protocolVersion": jsonrpc.LATEST_PROTOCOL_VERSION,
				"clientInfo": map[string]interface{}{
					"name":    jsonrpc.PROXY_CLIENT_NAME,
					"version": jsonrpc.PROXY_CLIENT_VERSION,
				},
				"capabilities": map[string]interface{}{},
			},
		}
		initBytes, _ := json.Marshal(initReq)
		initReq_http, _ := http.NewRequest("POST", serverkey.StreamableHttpUrl, bytes.NewReader(initBytes))
		initReq_http.Header = headers
		global.GVA_LOG.Info("[fetchToolsList] HTTP initialize请求", zap.String("url", serverkey.StreamableHttpUrl), zap.ByteString("body", initBytes))

		initResp, err := client.Do(initReq_http)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] HTTP initialize请求失败", zap.Error(err))
			return nil, fmt.Errorf("HTTP initialize请求失败: %v", err)
		}
		defer initResp.Body.Close()

		if initResp.StatusCode != 200 {
			initRespBody, _ := io.ReadAll(initResp.Body)
			global.GVA_LOG.Error("[fetchToolsList] HTTP initialize失败", zap.Int("statusCode", initResp.StatusCode), zap.String("body", string(initRespBody)))
			return nil, fmt.Errorf("HTTP initialize失败: %s", initResp.Status)
		}

		initRespBody, err := io.ReadAll(initResp.Body)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] 读取initialize响应失败", zap.Error(err))
			return nil, fmt.Errorf("读取initialize响应失败: %v", err)
		}

		var initRespObj map[string]interface{}
		if err := json.Unmarshal(initRespBody, &initRespObj); err != nil {
			global.GVA_LOG.Error("[fetchToolsList] initialize响应解析失败", zap.Error(err))
			return nil, fmt.Errorf("initialize响应解析失败: %v", err)
		}

		if errVal, ok := initRespObj["error"]; ok && errVal != nil {
			global.GVA_LOG.Error("[fetchToolsList] initialize返回错误", zap.Any("error", errVal))
			return nil, fmt.Errorf("initialize返回错误: %v", errVal)
		}

		global.GVA_LOG.Info("[fetchToolsList] HTTP initialize成功")

		// 2. 发送 notifications/initialized 通知
		notificationReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"method":  jsonrpc.MethodInitializedNotification,
			"params":  map[string]interface{}{},
		}
		notificationBytes, _ := json.Marshal(notificationReq)
		notificationReq_http, _ := http.NewRequest("POST", serverkey.StreamableHttpUrl, bytes.NewReader(notificationBytes))
		notificationReq_http.Header = headers
		_, err = client.Do(notificationReq_http)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] HTTP notifications/initialized失败", zap.Error(err))
			return nil, fmt.Errorf("HTTP notifications/initialized失败: %v", err)
		}
		global.GVA_LOG.Info("[fetchToolsList] HTTP 已发送notifications/initialized通知")

		// 3. 发送 tools/list 请求
		requestID++ // tools/list 使用 ID 1
		listReq := map[string]interface{}{
			"jsonrpc": jsonrpc.JSONRPC_VERSION,
			"id":      requestID,
			"method":  jsonrpc.MethodListTools,
			"params":  map[string]interface{}{},
		}
		listBytes, _ := json.Marshal(listReq)
		req, _ := http.NewRequest("POST", serverkey.StreamableHttpUrl, bytes.NewReader(listBytes))
		req.Header = headers
		global.GVA_LOG.Info("[fetchToolsList] streamableHttpUrl listTools请求", zap.String("url", serverkey.StreamableHttpUrl), zap.ByteString("body", listBytes))
		resp, err := client.Do(req)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] streamableHttpUrl请求失败", zap.Error(err))
			return nil, err
		}
		defer resp.Body.Close()

		// Check HTTP status code first
		if resp.StatusCode != 200 {
			respBody, _ := io.ReadAll(resp.Body)
			global.GVA_LOG.Error("[fetchToolsList] HTTP请求失败", zap.Int("statusCode", resp.StatusCode), zap.String("status", resp.Status), zap.String("body", string(respBody)))
			return nil, fmt.Errorf("HTTP请求失败: %s, 响应: %s", resp.Status, string(respBody))
		}

		// 先读取响应体内容进行分析
		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsList] 读取响应体失败", zap.Error(err))
			return nil, err
		}

		global.GVA_LOG.Info("[fetchToolsList] 原始响应", zap.String("status", resp.Status), zap.Int("statusCode", resp.StatusCode), zap.String("body", string(respBody)))

		var respObj map[string]interface{}

		// 检查响应内容是否包含 SSE 格式的标识符
		bodyStr := string(respBody)
		if strings.Contains(bodyStr, "id:") && strings.Contains(bodyStr, "data:") {
			global.GVA_LOG.Info("[fetchToolsList] 检测到SSE格式响应，开始解析")

			// 按行分割响应内容
			lines := strings.Split(bodyStr, "\n")
			var jsonData string

			for _, line := range lines {
				line = strings.TrimSpace(line)
				global.GVA_LOG.Debug("[fetchToolsList] SSE行", zap.String("line", line))

				// 查找 data: 开头的行
				if strings.HasPrefix(line, "data:") {
					data := strings.TrimSpace(strings.TrimPrefix(line, "data:"))
					if data != "" && data != "[DONE]" {
						// 尝试解析为 JSON
						var testObj map[string]interface{}
						if err := json.Unmarshal([]byte(data), &testObj); err == nil {
							jsonData = data
							global.GVA_LOG.Info("[fetchToolsList] 找到有效的JSON数据", zap.String("data", data))
							break
						}
					}
				}
			}

			if jsonData == "" {
				global.GVA_LOG.Error("[fetchToolsList] SSE响应中未找到有效的JSON数据")
				return nil, fmt.Errorf("SSE响应中未找到有效的JSON数据")
			}

			// 解析提取的 JSON 数据
			if err := json.Unmarshal([]byte(jsonData), &respObj); err != nil {
				global.GVA_LOG.Error("[fetchToolsList] SSE JSON解析失败", zap.Error(err), zap.String("jsonData", jsonData))
				return nil, fmt.Errorf("SSE JSON解析失败: %v, 数据: %s", err, jsonData)
			}
		} else {
			// 按普通 JSON 响应处理
			global.GVA_LOG.Info("[fetchToolsList] 检测到JSON响应，开始解析")

			if err := json.Unmarshal(respBody, &respObj); err != nil {
				global.GVA_LOG.Error("[fetchToolsList] JSON响应解析失败", zap.Error(err), zap.String("rawBody", string(respBody)))
				return nil, fmt.Errorf("JSON解析失败: %v, 原始响应: %s", err, string(respBody))
			}
		}

		global.GVA_LOG.Info("[fetchToolsList] streamableHttpUrl响应", zap.Any("resp", respObj))

		if errVal, ok := respObj["error"]; ok && errVal != nil {
			global.GVA_LOG.Error("[fetchToolsList] tools/list返回错误", zap.Any("error", errVal))
			return nil, fmt.Errorf("tools/list返回错误: %v", errVal)
		}

		result, ok := respObj["result"].(map[string]interface{})
		if !ok {
			global.GVA_LOG.Error("[fetchToolsList] tools/list响应格式错误")
			return nil, fmt.Errorf("tools/list响应格式错误")
		}

		tools, ok = result["tools"].([]interface{})
		if !ok {
			global.GVA_LOG.Error("[fetchToolsList] tools/list响应缺少tools字段")
			return nil, fmt.Errorf("tools/list响应缺少tools字段")
		}

		global.GVA_LOG.Info("[fetchToolsList] HTTP成功获取工具列表", zap.Int("count", len(tools)))
		return tools, nil
	} else {
		// ===== 本地cmd逻辑 =====
		global.GVA_LOG.Info("[fetchToolsListTx] 使用本地CMD模式获取工具列表",
			zap.String("command", project.Command),
			zap.Bool("hasServerkey", serverkey != nil))
		var cmd string
		var envParts []string
		var argsParts []string
		// 处理env
		if project.EnvReal != "" && project.EnvReal != "\"\"" {
			var envMap map[string]string
			err := json.Unmarshal([]byte(project.EnvReal), &envMap)
			if err == nil {
				for k, v := range envMap {
					if v == "" {
						v = "key"
					}
					envParts = append(envParts, fmt.Sprintf("%s=%s", k, v))
				}
			}
		}
		// 处理args
		if project.Args != "" && project.Args != "\"\"" {
			var argsArr []string
			err := json.Unmarshal([]byte(project.Args), &argsArr)
			if err == nil {
				argsParts = append(argsParts, argsArr...)
			}
		}
		cmd = project.Command
		if len(argsParts) > 0 {
			cmd = fmt.Sprintf("%s %s", cmd, strings.Join(argsParts, " "))
		}
		if len(envParts) > 0 {
			// 兼容 JSON 环境变量自动加单引号
			for i, env := range envParts {
				kv := strings.SplitN(env, "=", 2)
				if len(kv) == 2 {
					val := strings.TrimSpace(kv[1])
					if strings.HasPrefix(val, "{") && strings.HasSuffix(val, "}") {
						// 用单引号包裹整个 value
						envParts[i] = fmt.Sprintf("%s='%s'", kv[0], val)
					}
				}
			}
			cmd = fmt.Sprintf("%s %s", strings.Join(envParts, " "), cmd)
		}
		serverConfig := &mcpserver.ServerConfig{
			Command: cmd,
		}
		global.GVA_LOG.Info("[fetchToolsListTx] 本地cmd处理后的命令", zap.String("cmd", cmd))
		client, err := mcpclient.NewClient(serverConfig)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsListTx] 创建本地客户端失败", zap.Error(err))
			return nil, err
		}
		defer client.Close()
		if err := client.Error(); err != nil {
			global.GVA_LOG.Error("[fetchToolsListTx] 本地客户端运行错误", zap.Error(err))
			return nil, err
		}
		global.GVA_LOG.Info("[fetchToolsListTx] 开始初始化本地客户端")
		initParams := &jsonrpc.InitializeParams{
			ProtocolVersion: jsonrpc.LATEST_PROTOCOL_VERSION,
			ClientInfo: jsonrpc.ClientInfo{
				Name:    jsonrpc.PROXY_CLIENT_NAME,
				Version: jsonrpc.PROXY_CLIENT_VERSION,
			},
			Capabilities: jsonrpc.ClientCapabilities{},
		}
		initResult, err := client.Initialize(initParams)
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsListTx] 初始化本地客户端失败", zap.Error(err))
			return nil, err
		}
		// ===== 新增：自动更新项目version字段 =====
		version := ""
		if initResult.ServerInfo.Version != "" {
			version = initResult.ServerInfo.Version
		}
		if version != "" {
			global.GVA_LOG.Info("[fetchToolsListTx] 检测到工具版本号，准备更新projects.version", zap.String("cmd", cmd), zap.String("version", version))
			err := tx.Model(project).Update("version", version).Error
			if err != nil {
				global.GVA_LOG.Error("[fetchToolsListTx] 更新项目version失败", zap.Error(err), zap.String("cmd", cmd), zap.String("version", version))
			} else {
				global.GVA_LOG.Info("[fetchToolsListTx] 已自动更新项目version字段", zap.String("version", version))
			}
		}
		if err := client.NotificationsInitialized(); err != nil {
			global.GVA_LOG.Error("[fetchToolsListTx] 发送初始化通知失败", zap.Error(err))
			return nil, err
		}
		global.GVA_LOG.Info("[fetchToolsListTx] 开始获取本地工具列表")
		toolsResultPtr, err := client.ListTools()
		if err != nil {
			global.GVA_LOG.Error("[fetchToolsListTx] 获取本地工具列表失败", zap.Error(err))
			return nil, err
		}
		for _, tool := range toolsResultPtr.Tools {
			// 组装成[]interface{}返回
			toolMap := map[string]interface{}{
				"name":         tool.Name,
				"description":  tool.Description,
				"inputSchema":  tool.InputSchema,
				"outputSchema": tool.OutputSchema,
			}
			tools = append(tools, toolMap)
		}
		return tools, nil
	}
	global.GVA_LOG.Error("[fetchToolsListTx] 未配置有效的CMD、SSE或streamableHttpUrl")
	return nil, fmt.Errorf("未配置有效的CMD、SSE或streamableHttpUrl")
}

func InsertToolsForProjectTx(tx *gorm.DB, project *mcp.Projects, tools []interface{}) error {
	// 优化：减少锁定时间，先快速查询必要数据，然后批量操作

	// 1. 快速查出原有工具的关键信息映射
	var oldTools []mcp.ProjectTools
	pointsMap := make(map[string]int)
	cnameMap := make(map[string]string)
	descriptionMap := make(map[string]string)
	isSingleCallMap := make(map[string]int)
	supportedExtensionsMap := make(map[string]datatypes.JSON)
	multiFileTypeMap := make(map[string]int)
	keywordsMap := make(map[string]string)
	platformsMap := make(map[string]datatypes.JSON)
	canHandleDirectoryMap := make(map[string]int)
	prerequisiteToolIdMap := make(map[string]int)
	canDirectExecuteMap := make(map[string]int)
	isDangerousMap := make(map[string]int)
	isDisabledMap := make(map[string]int)
	// 新增字段映射
	isErrorMap := make(map[string]int)
	isRequiredParameterMap := make(map[string]int)
	logoMap := make(map[string]string)

	if err := tx.Where("project_id = ?", project.ID).Find(&oldTools).Error; err == nil {
		for _, t := range oldTools {
			if t.Name != nil {
				pointsMap[*t.Name] = 0
				if t.Points != nil {
					pointsMap[*t.Name] = *t.Points
				}
				if t.CName != nil {
					cnameMap[*t.Name] = *t.CName
				}
				if t.DescriptionChinese != nil {
					descriptionMap[*t.Name] = *t.DescriptionChinese
				}
				if t.IsSingleCall != nil {
					isSingleCallMap[*t.Name] = *t.IsSingleCall
				}
				if t.SupportedExtensions != nil {
					supportedExtensionsMap[*t.Name] = t.SupportedExtensions
				}
				if t.MultiFileType != nil {
					multiFileTypeMap[*t.Name] = *t.MultiFileType
				}
				if t.Keywords != nil {
					keywordsMap[*t.Name] = *t.Keywords
				}
				if t.Platforms != nil {
					platformsMap[*t.Name] = t.Platforms
				}
				if t.CanHandleDirectory != nil {
					canHandleDirectoryMap[*t.Name] = *t.CanHandleDirectory
				}
				if t.PrerequisiteToolId != nil {
					prerequisiteToolIdMap[*t.Name] = *t.PrerequisiteToolId
				}
				if t.CanDirectExecute != nil {
					canDirectExecuteMap[*t.Name] = *t.CanDirectExecute
				}
				if t.IsDangerous != nil {
					isDangerousMap[*t.Name] = *t.IsDangerous
				}
				if t.IsDisabled != nil {
					isDisabledMap[*t.Name] = *t.IsDisabled
				}
				if t.IsError != nil {
					isErrorMap[*t.Name] = *t.IsError
				}
				if t.IsRequiredParameter != nil {
					isRequiredParameterMap[*t.Name] = *t.IsRequiredParameter
				}
				if t.Logo != nil {
					logoMap[*t.Name] = *t.Logo
				}
			}
		}
	}

	// 2. 优化：使用批量删除，减少锁定时间
	if err := tx.Unscoped().Where("project_id = ?", project.ID).Delete(&mcp.ProjectTools{}).Error; err != nil {
		global.GVA_LOG.Error("删除项目工具失败", zap.Error(err), zap.Uint("projectId", project.ID))
		return fmt.Errorf("删除项目工具失败: %v", err)
	}

	// 3. 优化：批量准备新工具数据，然后一次性插入
	var newTools []mcp.ProjectTools
	for _, t := range tools {
		tool, _ := t.(map[string]interface{})
		name, _ := tool["name"].(string)
		desc, _ := tool["description"].(string)
		inputSchema, _ := json.Marshal(tool["inputSchema"])
		outputSchema, _ := json.Marshal(tool["outputSchema"])
		inputSchemaJSON := datatypes.JSON(inputSchema)
		outputSchemaJSON := datatypes.JSON(outputSchema)
		projectId := int(project.ID)
		projectUUId := project.UUID
		points := 0
		if old, ok := pointsMap[name]; ok {
			points = old
		}
		var cName *string
		if newCName, ok := tool["c_name"].(string); ok && newCName != "" {
			// 新c_name不为空时，使用新值
			cName = &newCName
		} else if oldCName, ok := cnameMap[name]; ok {
			// 新c_name为空时，保持原有值不变
			cName = &oldCName
		}

		var descriptionChinese *string
		if newDescChinese, ok := tool["descriptionChinese"].(string); ok && newDescChinese != "" {
			// 新descriptionChinese不为空时，使用新值
			descriptionChinese = &newDescChinese
		} else if oldDescriptionChinese, ok := descriptionMap[name]; ok {
			// 新descriptionChinese为空时，保持原有值不变
			descriptionChinese = &oldDescriptionChinese
		}

		isSingleCall := 1 // 默认值为1
		if newIsSingleCall, ok := tool["is_single_call"].(float64); ok {
			// 新is_single_call有值时，使用新值
			isSingleCall = int(newIsSingleCall)
		} else if oldIsSingleCall, ok := isSingleCallMap[name]; ok {
			// 新is_single_call无值时，保持原有值不变
			isSingleCall = oldIsSingleCall
		}

		// 处理新增字段
		var supportedExtensions datatypes.JSON
		if newSupportedExt, ok := tool["supportedExtensions"]; ok && newSupportedExt != nil {
			// 新supportedExtensions有值时，使用新值
			if extBytes, err := json.Marshal(newSupportedExt); err == nil {
				supportedExtensions = datatypes.JSON(extBytes)
			} else {
				supportedExtensions = datatypes.JSON("[]")
			}
		} else if oldSupportedExtensions, ok := supportedExtensionsMap[name]; ok {
			// 新supportedExtensions无值时，保持原有值不变
			supportedExtensions = oldSupportedExtensions
		} else {
			// 默认为空数组
			supportedExtensions = datatypes.JSON("[]")
		}

		multiFileType := 0 // 默认值为0（单文件）
		if newMultiFileType, ok := tool["multiFileType"].(float64); ok {
			// 新multiFileType有值时，使用新值
			multiFileType = int(newMultiFileType)
		} else if oldMultiFileType, ok := multiFileTypeMap[name]; ok {
			// 新multiFileType无值时，保持原有值不变
			multiFileType = oldMultiFileType
		}

		var keywords *string
		if newKeywords, ok := tool["keywords"].(string); ok && newKeywords != "" {
			// 新keywords不为空时，使用新值
			keywords = &newKeywords
		} else if oldKeywords, ok := keywordsMap[name]; ok {
			// 新keywords为空时，保持原有值不变
			keywords = &oldKeywords
		}

		var platforms datatypes.JSON
		if newPlatforms, ok := tool["platforms"]; ok && newPlatforms != nil {
			// 新platforms有值时，使用新值
			if platformBytes, err := json.Marshal(newPlatforms); err == nil {
				platforms = datatypes.JSON(platformBytes)
			} else {
				platforms = datatypes.JSON("[]")
			}
		} else if oldPlatforms, ok := platformsMap[name]; ok {
			// 新platforms无值时，保持原有值不变
			platforms = oldPlatforms
		} else {
			// 默认为空数组
			platforms = datatypes.JSON("[]")
		}

		canHandleDirectory := 0 // 默认值为0（不可处理目录）
		if newCanHandleDir, ok := tool["canHandleDirectory"].(float64); ok {
			// 新canHandleDirectory有值时，使用新值
			canHandleDirectory = int(newCanHandleDir)
		} else if oldCanHandleDirectory, ok := canHandleDirectoryMap[name]; ok {
			// 新canHandleDirectory无值时，保持原有值不变
			canHandleDirectory = oldCanHandleDirectory
		}

		var prerequisiteToolId *int
		if newPrereqToolId, ok := tool["prerequisiteToolId"].(float64); ok {
			// 新prerequisiteToolId有值时，使用新值
			intVal := int(newPrereqToolId)
			prerequisiteToolId = &intVal
		} else if oldPrerequisiteToolId, ok := prerequisiteToolIdMap[name]; ok {
			// 新prerequisiteToolId无值时，保持原有值不变
			prerequisiteToolId = &oldPrerequisiteToolId
		}

		canDirectExecute := 0 // 默认值为0（不可直接执行）
		if newCanDirectExec, ok := tool["canDirectExecute"].(float64); ok {
			// 新canDirectExecute有值时，使用新值
			canDirectExecute = int(newCanDirectExec)
		} else if oldCanDirectExecute, ok := canDirectExecuteMap[name]; ok {
			// 新canDirectExecute无值时，保持原有值不变
			canDirectExecute = oldCanDirectExecute
		}

		isDangerous := 0 // 默认值为0（非危险操作）
		if newIsDangerous, ok := tool["isDangerous"].(float64); ok {
			// 新isDangerous有值时，使用新值
			isDangerous = int(newIsDangerous)
		} else if oldIsDangerous, ok := isDangerousMap[name]; ok {
			// 新isDangerous无值时，保持原有值不变
			isDangerous = oldIsDangerous
		}

		isDisabled := 0 // 默认值为0（未禁用）
		if newIsDisabled, ok := tool["isDisabled"].(float64); ok {
			// 新isDisabled有值时，使用新值
			isDisabled = int(newIsDisabled)
		} else if oldIsDisabled, ok := isDisabledMap[name]; ok {
			// 新isDisabled无值时，保持原有值不变
			isDisabled = oldIsDisabled
		}

		// 新增字段：是否质检不通过、是否必填参数、logo
		isError := 0 // 默认0
		if newIsError, ok := tool["isError"].(float64); ok {
			// 新isError有值时，使用新值
			isError = int(newIsError)
		} else if oldIsError, ok := isErrorMap[name]; ok {
			// 新isError无值时，保持原有值不变
			isError = oldIsError
		}

		isRequiredParameter := 0 // 默认0
		if newIsReqParam, ok := tool["isRequiredParameter"].(float64); ok {
			// 新isRequiredParameter有值时，使用新值
			isRequiredParameter = int(newIsReqParam)
		} else if oldIsRequiredParameter, ok := isRequiredParameterMap[name]; ok {
			// 新isRequiredParameter无值时，保持原有值不变
			isRequiredParameter = oldIsRequiredParameter
		}
		var logo *string
		if newLogo, ok := tool["logo"].(string); ok && newLogo != "" {
			// 新logo不为空时，使用新logo（覆盖旧的）
			logo = &newLogo
		} else if oldLogo, ok := logoMap[name]; ok {
			// 新logo为空时，保持原有logo不变
			logo = &oldLogo
		}

		projectTool := mcp.ProjectTools{
			ProjectId:           &projectId,
			ProjectUUId:         &projectUUId,
			Name:                &name,
			Description:         &desc,
			DescriptionChinese:  descriptionChinese,
			InputSchema:         inputSchemaJSON,
			OutputSchema:        outputSchemaJSON,
			Points:              &points,
			CName:               cName,
			IsSingleCall:        &isSingleCall,
			SupportedExtensions: supportedExtensions,
			MultiFileType:       &multiFileType,
			Keywords:            keywords,
			Platforms:           platforms,
			CanHandleDirectory:  &canHandleDirectory,
			PrerequisiteToolId:  prerequisiteToolId,
			CanDirectExecute:    &canDirectExecute,
			IsDangerous:         &isDangerous,
			IsDisabled:          &isDisabled,
			IsError:             &isError,
			IsRequiredParameter: &isRequiredParameter,
			Logo:                logo,
		}
		// 添加到批量插入数组中
		newTools = append(newTools, projectTool)
	}

	// 4. 批量插入所有工具，减少数据库操作次数
	if len(newTools) > 0 {
		// 分批插入，每次最多插入100条记录，避免单次插入过多数据
		batchSize := 100
		for i := 0; i < len(newTools); i += batchSize {
			end := i + batchSize
			if end > len(newTools) {
				end = len(newTools)
			}
			batch := newTools[i:end]
			if err := tx.Create(&batch).Error; err != nil {
				global.GVA_LOG.Error("批量插入工具失败", zap.Error(err), zap.Int("batchStart", i), zap.Int("batchEnd", end))
				return fmt.Errorf("批量插入工具失败: %v", err)
			}
		}
		global.GVA_LOG.Info("批量插入工具成功", zap.Int("totalCount", len(newTools)))
	}

	return nil
}

// IsProjectLiked 查询当前登录用户是否对指定项目点赞
// @Tags Projects
// @Summary 查询当前登录用户是否对指定项目点赞
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param projectID query uint true "项目ID"
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /projects/isLiked [get]
func (projectsApi *ProjectsApi) IsProjectLiked(c *gin.Context) {
	projectIDStr := c.Query("projectID")
	if projectIDStr == "" {
		response.FailWithMessage("projectID不能为空", c)
		return
	}
	projectID, err := strconv.ParseUint(projectIDStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的项目ID", c)
		return
	}
	userUUID := utils.GetUserUuid(c)
	isLiked := false
	if userUUID != uuid.Nil {
		isLiked, err = projectsService.CheckProjectLiked(c, uint(projectID), userUUID)
		if err != nil {
			global.GVA_LOG.Error("检查项目点赞状态失败!", zap.Error(err))
			response.FailWithMessage("查询失败", c)
			return
		}
	}

	// 查询项目详情
	project, err := projectsService.GetProjects(c.Request.Context(), projectIDStr)
	if err != nil {
		global.GVA_LOG.Error("查询项目失败!", zap.Error(err))
		response.FailWithMessage("查询项目失败", c)
		return
	}

	sseUrl := ""
	streamableHttpUrl := ""
	apiKey := ""
	if userUUID != uuid.Nil {
		userId := utils.GetUserID(c)
		if userId != 0 {
			if keyObj, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.FindLatestApiKey(userId); err == nil && keyObj != nil {
				apiKey = keyObj.ApiKey
			}
		}
	}
	if project.BaseUrl != "" {
		if apiKey != "" {
			sseUrl = project.BaseUrl + "/" + apiKey
			streamableHttpUrl = project.BaseUrl
			if strings.Contains(streamableHttpUrl, "/sse/") {
				streamableHttpUrl = strings.Replace(streamableHttpUrl, "/sse/", "/mcp/", 1)
			}
			streamableHttpUrl = streamableHttpUrl + "/" + apiKey
		} else {
			sseUrl = project.BaseUrl
			streamableHttpUrl = project.BaseUrl
			if strings.Contains(streamableHttpUrl, "/sse/") {
				streamableHttpUrl = strings.Replace(streamableHttpUrl, "/sse/", "/mcp/", 1)
			}
		}
	}

	serverKey, err := mcprouter.FindServerkeyByServerUUID(project.UUID)
	serverCommandNotEmpty := false
	if err == nil && serverKey != nil && serverKey.ServerCommand != "" {
		serverCommandNotEmpty = true
	}

	resp := gin.H{"isLiked": isLiked}
	if serverCommandNotEmpty {
		resp["sseUrl"] = sseUrl
		resp["streamableHttpUrl"] = streamableHttpUrl
	} else {
		if err == nil && serverKey != nil && serverKey.SseUrl != "" {
			resp["sseUrl"] = sseUrl
		} else {
			resp["sseUrl"] = ""
		}
		if err == nil && serverKey != nil && serverKey.StreamableHttpUrl != "" {
			resp["streamableHttpUrl"] = streamableHttpUrl
		} else {
			resp["streamableHttpUrl"] = ""
		}
	}
	callType := 0
	if serverCommandNotEmpty {
		resp["sseUrl"] = sseUrl
		resp["streamableHttpUrl"] = streamableHttpUrl
		if sseUrl != "" && streamableHttpUrl != "" {
			callType = 3
		} else if sseUrl != "" {
			callType = 1
		} else if streamableHttpUrl != "" {
			callType = 2
		}
	} else {
		if err == nil && serverKey != nil && serverKey.SseUrl != "" {
			resp["sseUrl"] = sseUrl
			callType += 1
		} else {
			resp["sseUrl"] = ""
		}
		if err == nil && serverKey != nil && serverKey.StreamableHttpUrl != "" {
			resp["streamableHttpUrl"] = streamableHttpUrl
			callType += 2
		} else {
			resp["streamableHttpUrl"] = ""
		}
	}
	resp["callType"] = callType
	response.OkWithData(resp, c)
}

// TestMCPMonitor 手动触发 MCP 连接监控测试
// @Tags Projects
// @Summary 手动触发 MCP 连接监控测试
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "测试完成"
// @Router /projects/testMCPMonitor [post]
func (projectsApi *ProjectsApi) TestMCPMonitor(c *gin.Context) {
	global.GVA_LOG.Info("[手动测试] 开始 MCP 连接监控")

	// 查询所有托管的 MCP 项目（base_url 不为空且已启用，排除 baidu-youxuan）
	var projects []mcp.Projects
	err := global.GVA_DB.Where("base_url != '' AND base_url IS NOT NULL AND is_enabled = 1 AND status = 'created' AND uuid != 'baidu-youxuan'").Find(&projects).Error
	if err != nil {
		global.GVA_LOG.Error("查询托管 MCP 项目失败", zap.Error(err))
		response.FailWithMessage("查询项目失败:"+err.Error(), c)
		return
	}

	global.GVA_LOG.Info("[手动测试] 找到托管 MCP 项目", zap.Int("count", len(projects)))

	// 调试：打印找到的项目信息
	for i, p := range projects {
		global.GVA_LOG.Info("[手动测试] 项目信息",
			zap.Int("index", i),
			zap.String("name", p.Name),
			zap.String("uuid", p.UUID),
			zap.String("baseUrl", p.BaseUrl),
			zap.String("callMethod", p.CallMethod),
			zap.Bool("isEnabled", p.IsEnabled))
	}

	var results []map[string]interface{}
	var failedConnections []string
	token := "70101873-00ce-4115-b3d3-51495d6b77e2"
	domain := "https://www.mcpcn.cc"

	for _, project := range projects {
		// 构造连接URL - 支持SSE和HTTP两种方式
		var sseURL, httpURL string
		var connectionType string
		var testURL string

		// 检查项目是否有proxy_sse_url或proxy_http_url配置
		if project.ProxySseUrl != "" {
			sseURL = project.ProxySseUrl
			testURL = sseURL
			connectionType = "SSE(代理)"
		} else if project.ProxyHttpUrl != "" {
			httpURL = project.ProxyHttpUrl
			testURL = httpURL
			connectionType = "HTTP(代理)"
		} else {
			// 默认构造SSE URL
			sseURL = fmt.Sprintf("%s%s/%s", domain, project.BaseUrl, token)
			testURL = sseURL
			connectionType = "SSE(默认)"
		}

		global.GVA_LOG.Info("[手动测试] 测试 MCP 连接",
			zap.String("project", project.Name),
			zap.String("type", connectionType),
			zap.String("url", testURL))

		result := map[string]interface{}{
			"name":           project.Name,
			"uuid":           project.UUID,
			"url":            testURL,
			"connectionType": connectionType,
			"status":         "success",
			"message":        "",
			"tools":          0,
		}

		// 创建临时 server_keys 记录用于测试
		// 注意：托管服务通常不需要额外的环境变量，因为 token 已经在 URL 中
		tempServerKey := mcprouter.Serverkey{
			ServerKey:         project.UUID,
			ServerUUID:        project.UUID,
			ServerName:        project.Name,
			SseUrl:            sseURL,
			StreamableHttpUrl: httpURL,
			Status:            "created",
			EnvJson:           project.EnvReal,
			CreatedAt:         time.Now(),
		}

		global.GVA_LOG.Info("[手动测试] 创建临时 server_keys",
			zap.String("uuid", project.UUID),
			zap.String("connectionType", connectionType),
			zap.String("sseUrl", sseURL),
			zap.String("httpUrl", httpURL))

		// 创建临时项目对象用于测试
		testProject := mcp.Projects{
			UUID:       project.UUID,
			Name:       project.Name,
			CallMethod: "online",
		}

		// 在事务中先删除可能存在的旧记录，然后创建临时记录并测试
		tx := global.GVA_DB.Begin()

		// 删除可能存在的旧记录
		tx.Where("server_uuid = ?", project.UUID).Delete(&mcprouter.Serverkey{})

		// 创建临时记录
		createResult := tx.Create(&tempServerKey)
		if createResult.Error != nil {
			global.GVA_LOG.Error("[手动测试] 创建临时 server_keys 失败", zap.Error(createResult.Error))
			tx.Rollback()
			result["status"] = "failed"
			result["message"] = "创建临时记录失败: " + createResult.Error.Error()
			results = append(results, result)
			continue
		}

		global.GVA_LOG.Info("[手动测试] 临时 server_keys 创建成功", zap.Int64("id", tempServerKey.ID))

		// 尝试获取工具列表
		tools, err := FetchToolsListTx(tx, &testProject)
		tx.Rollback() // 测试完成后回滚，不保存数据

		if err != nil {
			global.GVA_LOG.Error("[手动测试] MCP 连接失败",
				zap.String("project", project.Name),
				zap.String("uuid", project.UUID),
				zap.String("connectionType", connectionType),
				zap.String("url", testURL),
				zap.Error(err))

			result["status"] = "failed"
			result["message"] = err.Error()
			failedConnections = append(failedConnections, fmt.Sprintf("• %s (%s) [%s]: %s", project.Name, project.UUID, connectionType, err.Error()))
		} else {
			global.GVA_LOG.Info("[手动测试] MCP 连接成功",
				zap.String("project", project.Name),
				zap.String("connectionType", connectionType),
				zap.Int("tools", len(tools)))

			result["tools"] = len(tools)
			result["message"] = fmt.Sprintf("成功获取 %d 个工具", len(tools))
		}

		results = append(results, result)

		// 避免请求过于频繁
		time.Sleep(1 * time.Second)
	}

	// 统计结果
	successCount := len(projects) - len(failedConnections)
	global.GVA_LOG.Info("[手动测试] 监控结果统计",
		zap.Int("total", len(projects)),
		zap.Int("success", successCount),
		zap.Int("failed", len(failedConnections)))

	// 分类错误类型
	var criticalErrors []string
	var minorErrors []string

	for _, failure := range failedConnections {
		if strings.Contains(failure, "SSE未获取到endpoint") ||
			strings.Contains(failure, "未收到initialize响应") {
			criticalErrors = append(criticalErrors, failure)
		} else if strings.Contains(failure, "Invalid request parameters") {
			minorErrors = append(minorErrors, failure)
		} else {
			criticalErrors = append(criticalErrors, failure)
		}
	}

	global.GVA_LOG.Info("[手动测试] 错误分类",
		zap.Int("critical", len(criticalErrors)),
		zap.Int("minor", len(minorErrors)))

	// 只有严重错误才发送飞书通知
	if len(criticalErrors) > 0 {
		global.GVA_LOG.Info("[手动测试] 发现严重连接问题", zap.Int("count", len(criticalErrors)))

		message := fmt.Sprintf("🚨 MCP 连接监控告警（手动测试）\n\n检测时间: %s\n总计: %d 个项目\n✅ 成功: %d 个\n❌ 严重错误: %d 个\n⚠️ 轻微错误: %d 个\n\n严重错误详情:\n%s",
			time.Now().Format("2006-01-02 15:04:05"),
			len(projects),
			successCount,
			len(criticalErrors),
			len(minorErrors),
			strings.Join(criticalErrors, "\n"))

		err := sendFeishuNotification(message)
		if err != nil {
			global.GVA_LOG.Error("[手动测试] 发送飞书通知失败", zap.Error(err))
		} else {
			global.GVA_LOG.Info("[手动测试] 飞书通知发送成功")
		}
	} else if len(minorErrors) > 0 {
		global.GVA_LOG.Info("[手动测试] 仅发现轻微问题，不发送通知", zap.Int("count", len(minorErrors)))
	} else {
		global.GVA_LOG.Info("[手动测试] 所有连接正常")
	}

	responseData := map[string]interface{}{
		"total":   len(projects),
		"failed":  len(failedConnections),
		"success": len(projects) - len(failedConnections),
		"results": results,
	}

	response.OkWithDetailed(responseData, "MCP 连接监控测试完成", c)
}

// 飞书机器人通知结构体
type FeishuMessage struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

// 发送飞书机器人通知
func sendFeishuNotification(message string) error {
	webhookURL := "https://open.feishu.cn/open-apis/bot/v2/hook/215def92-0f5f-45f8-8a69-7376b62043be"

	msg := FeishuMessage{
		MsgType: "text",
	}
	msg.Content.Text = message

	jsonData, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	global.GVA_LOG.Info("飞书通知发送", zap.String("message", message), zap.Int("status", resp.StatusCode))
	return nil
}

// clearSensitiveFields 清除项目的敏感字段
func clearSensitiveFields(project *mcp.Projects) {
	if project == nil {
		return
	}
	project.EnvReal = ""
	project.ProxySseUrl = ""
	project.ProxyHttpUrl = ""
}

// clearSensitiveFieldsList 清除项目列表的敏感字段
func clearSensitiveFieldsList(projects []mcp.Projects) {
	for i := range projects {
		clearSensitiveFields(&projects[i])
	}
}

// FormatProjectsToToolLibrary 将项目搜索结果格式化为工具库列表文本
func FormatProjectsToToolLibrary(results []mcpService.ProjectSearchResult) string {
	var builder strings.Builder

	for _, project := range results {
		builder.WriteString(fmt.Sprintf("**%s** (ID: %s)\n", project.Name, project.UUID))
		if project.ToolNames != "" {
			builder.WriteString(fmt.Sprintf("   功能：%s\n", project.ToolNames))
		} else {
			builder.WriteString("   功能：暂无\n")
		}
		if strings.TrimSpace(project.LlmRemark) != "" {
			builder.WriteString(fmt.Sprintf("   备注：%s\n", project.LlmRemark))
		}
		builder.WriteString("\n")
	}

	return builder.String()
}

// FormatProjectsToSimpleList 将项目搜索结果格式化为简化文本列表
// 仅包含名称和ID，每行：**名称** (ID: uuid)
func FormatProjectsToSimpleList(results []mcpService.ProjectSearchResult) string {
	var builder strings.Builder
	for _, project := range results {
		suffix := ""
		if strings.TrimSpace(project.LlmRemark) != "" {
			suffix = fmt.Sprintf(" - %s", project.LlmRemark)
		}
		builder.WriteString(fmt.Sprintf("**%s** (ID: %s)%s\n", project.Name, project.UUID, suffix))
	}
	return builder.String()
}

// ProjectsSearch 工具库查询
// @Tags Projects
// @Summary 工具库查询
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]interface{},msg=string} "查询成功"
// @Router /projects/projectsSearch [post]
func (projectsApi *ProjectsApi) ProjectsSearch(c *gin.Context) {
	ctx := c.Request.Context()

	// 检查请求参数，判断返回格式
	format := c.Query("format")

	// 先尝试从缓存获取数据
	if global.GVA_REDIS != nil {
		if format == "grouped" {
			// 尝试获取分组缓存
			cacheKey := "mcp-tools-grouped"
			cachedData, err := global.GVA_REDIS.Get(ctx, cacheKey).Result()
			if err == nil && cachedData != "" {
				// 缓存命中，直接返回
				var groupedTools map[string][]interface{}
				if json.Unmarshal([]byte(cachedData), &groupedTools) == nil {
					response.OkWithData(groupedTools, c)
					return
				}
			}
		} else if format == "simple" {
			// 尝试获取简化文本缓存
			cacheKey := "mcp-tools-simple"
			cachedText, err := global.GVA_REDIS.Get(ctx, cacheKey).Result()
			if err == nil && cachedText != "" {
				response.OkWithData(cachedText, c)
				return
			}
		} else {
			// 尝试获取文本缓存
			cacheKey := "mcp-tools"
			cachedText, err := global.GVA_REDIS.Get(ctx, cacheKey).Result()
			if err == nil && cachedText != "" {
				// 缓存命中，直接返回
				response.OkWithData(cachedText, c)
				return
			}
		}
	}

	// 缓存未命中，需要重新生成所有缓存
	// 1. 生成分组格式数据
	var tools []mcp.ProjectTools
	db := global.GVA_DB.Joins("JOIN projects ON project_tools.project_uuid = projects.uuid").
		Where("project_tools.deleted_at IS NULL AND projects.deleted_at IS NULL AND projects.is_enabled = ? AND (project_tools.is_disabled = 0 OR project_tools.is_disabled IS NULL)", true)

	err := db.Find(&tools).Error
	if err != nil {
		global.GVA_LOG.Error("查询所有工具失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}

	// 按项目UUID分组工具
	groupedTools := make(map[string][]interface{})

	for _, tool := range tools {
		projectUUID := ""
		if tool.ProjectUUId != nil {
			projectUUID = *tool.ProjectUUId
		}

		// 构造工具对象
		toolObj := map[string]interface{}{
			"id":                  tool.ID,
			"projectUUId":         tool.ProjectUUId,
			"projectId":           tool.ProjectId,
			"name":                tool.Name,
			"description":         tool.Description,
			"descriptionChinese":  tool.DescriptionChinese,
			"inputSchema":         tool.InputSchema,
			"points":              tool.Points,
			"outputSchema":        tool.OutputSchema,
			"regex":               tool.Regex,
			"c_name":              tool.CName,
			"is_single_call":      tool.IsSingleCall,
			"supportedExtensions": tool.SupportedExtensions,
			"multiFileType":       tool.MultiFileType,
			"keywords":            tool.Keywords,
			"platforms":           tool.Platforms,
			"canHandleDirectory":  tool.CanHandleDirectory,
			"prerequisiteToolId":  tool.PrerequisiteToolId,
			"canDirectExecute":    tool.CanDirectExecute,
			"isDangerous":         tool.IsDangerous,
			"isDisabled":          tool.IsDisabled,
		}

		// 添加到对应项目的工具数组中
		if projectUUID != "" {
			groupedTools[projectUUID] = append(groupedTools[projectUUID], toolObj)
		}
	}

	// 2. 生成文本格式数据与简化文本（支持可选平台过滤）
	platform := c.Query("platform")
	var pfPtr *string
	if strings.TrimSpace(platform) != "" {
		pfPtr = &platform
	}
	results, err := projectsService.ProjectsSearch(ctx, pfPtr)
	if err != nil {
		global.GVA_LOG.Error("工具库查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	formattedText := FormatProjectsToToolLibrary(results)
	simpleText := FormatProjectsToSimpleList(results)

	// 3. 同时保存两个缓存到Redis
	if global.GVA_REDIS != nil {
		// 保存分组缓存
		groupedCacheKey := "mcp-tools-grouped"
		groupedToolsJSON, err := json.Marshal(groupedTools)
		if err == nil {
			global.GVA_REDIS.Set(ctx, groupedCacheKey, string(groupedToolsJSON), 0).Err()
		}

		// 保存文本缓存（总）
		textCacheKey := "mcp-tools"
		global.GVA_REDIS.Set(ctx, textCacheKey, formattedText, 0).Err()

		// 保存简化文本缓存（总）
		simpleCacheKey := "mcp-tools-simple"
		global.GVA_REDIS.Set(ctx, simpleCacheKey, simpleText, 0).Err()

		// 生成按平台的两个独立缓存 key：windows / mac
		for _, pf := range []string{"windows", "mac"} {
			pf := pf
			pfResults, err := projectsService.ProjectsSearch(ctx, &pf)
			if err == nil {
				pfText := FormatProjectsToToolLibrary(pfResults)
				pfSimple := FormatProjectsToSimpleList(pfResults)
				global.GVA_REDIS.Set(ctx, "mcp-tools:"+pf, pfText, 0).Err()
				global.GVA_REDIS.Set(ctx, "mcp-tools-simple:"+pf, pfSimple, 0).Err()
			}
		}
	}

	// 4. 根据请求格式返回对应数据
	if format == "grouped" {
		response.OkWithData(groupedTools, c)
	} else if format == "simple" {
		response.OkWithData(simpleText, c)
	} else {
		response.OkWithData(formattedText, c)
	}
}

// clearProjectsSearchCacheInternal 内部方法：清除工具库查询缓存并重新生成
func clearProjectsSearchCacheInternal(ctx context.Context) {
	if global.GVA_REDIS == nil {
		return // Redis未配置，跳过缓存清除
	}

	// 清除mcp-tools缓存
	cacheKey := "mcp-tools"
	err := global.GVA_REDIS.Del(ctx, cacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools缓存失败", zap.String("cacheKey", cacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools缓存成功", zap.String("cacheKey", cacheKey))
	}

	// 清除mcp-tools-grouped缓存
	groupedCacheKey := "mcp-tools-grouped"
	err = global.GVA_REDIS.Del(ctx, groupedCacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools-grouped缓存失败", zap.String("cacheKey", groupedCacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools-grouped缓存成功", zap.String("cacheKey", groupedCacheKey))
	}

	// 清除mcp-tools-simple缓存
	simpleCacheKey := "mcp-tools-simple"
	err = global.GVA_REDIS.Del(ctx, simpleCacheKey).Err()
	if err != nil {
		global.GVA_LOG.Error("自动清除mcp-tools-simple缓存失败", zap.String("cacheKey", simpleCacheKey), zap.Error(err))
	} else {
		global.GVA_LOG.Info("自动清除mcp-tools-simple缓存成功", zap.String("cacheKey", simpleCacheKey))
	}

	// 清除缓存后，异步重新生成缓存
	go func() {
		// 创建新的context用于异步操作
		asyncCtx := context.Background()

		// 重新生成所有缓存
		// 1. 生成分组格式数据
		var tools []mcp.ProjectTools
		db := global.GVA_DB.Joins("JOIN projects ON project_tools.project_uuid = projects.uuid").
			Where("project_tools.deleted_at IS NULL AND projects.deleted_at IS NULL AND projects.is_enabled = ? AND (project_tools.is_disabled = 0 OR project_tools.is_disabled IS NULL)", true)

		err := db.Find(&tools).Error
		if err != nil {
			global.GVA_LOG.Error("异步重新生成缓存时查询工具失败!", zap.Error(err))
			return
		}

		// 按项目UUID分组工具
		groupedTools := make(map[string][]interface{})

		for _, tool := range tools {
			projectUUID := ""
			if tool.ProjectUUId != nil {
				projectUUID = *tool.ProjectUUId
			}

			// 构造工具对象
			toolObj := map[string]interface{}{
				"id":                  tool.ID,
				"projectUUId":         tool.ProjectUUId,
				"projectId":           tool.ProjectId,
				"name":                tool.Name,
				"description":         tool.Description,
				"descriptionChinese":  tool.DescriptionChinese,
				"inputSchema":         tool.InputSchema,
				"points":              tool.Points,
				"outputSchema":        tool.OutputSchema,
				"regex":               tool.Regex,
				"c_name":              tool.CName,
				"is_single_call":      tool.IsSingleCall,
				"supportedExtensions": tool.SupportedExtensions,
				"multiFileType":       tool.MultiFileType,
				"keywords":            tool.Keywords,
				"platforms":           tool.Platforms,
				"canHandleDirectory":  tool.CanHandleDirectory,
				"prerequisiteToolId":  tool.PrerequisiteToolId,
				"canDirectExecute":    tool.CanDirectExecute,
				"isDangerous":         tool.IsDangerous,
				"isDisabled":          tool.IsDisabled,
			}

			// 添加到对应项目的工具数组中
			if projectUUID != "" {
				groupedTools[projectUUID] = append(groupedTools[projectUUID], toolObj)
			}
		}

		// 2. 生成文本格式数据与简化文本
		results, err := projectsService.ProjectsSearch(asyncCtx, nil)
		if err != nil {
			global.GVA_LOG.Error("异步重新生成缓存时查询项目失败!", zap.Error(err))
			return
		}
		formattedText := FormatProjectsToToolLibrary(results)
		simpleText := FormatProjectsToSimpleList(results)

		// 3. 同时保存两个缓存到Redis
		if global.GVA_REDIS != nil {
			// 保存分组缓存
			groupedCacheKey := "mcp-tools-grouped"
			groupedToolsJSON, err := json.Marshal(groupedTools)
			if err == nil {
				global.GVA_REDIS.Set(asyncCtx, groupedCacheKey, string(groupedToolsJSON), 0).Err()
				global.GVA_LOG.Info("异步重新生成mcp-tools-grouped缓存成功")
			}

			// 保存文本缓存
			textCacheKey := "mcp-tools"
			global.GVA_REDIS.Set(asyncCtx, textCacheKey, formattedText, 0).Err()
			global.GVA_LOG.Info("异步重新生成mcp-tools缓存成功")

			// 保存简化文本缓存
			simpleCacheKey := "mcp-tools-simple"
			global.GVA_REDIS.Set(asyncCtx, simpleCacheKey, simpleText, 0).Err()
			global.GVA_LOG.Info("异步重新生成mcp-tools-simple缓存成功")

			// 生成按平台的两个独立缓存 key：windows / mac
			for _, pf := range []string{"windows", "mac"} {
				pf := pf
				pfResults, err := projectsService.ProjectsSearch(asyncCtx, &pf)
				if err == nil {
					pfText := FormatProjectsToToolLibrary(pfResults)
					pfSimple := FormatProjectsToSimpleList(pfResults)
					global.GVA_REDIS.Set(asyncCtx, "mcp-tools:"+pf, pfText, 0).Err()
					global.GVA_REDIS.Set(asyncCtx, "mcp-tools-simple:"+pf, pfSimple, 0).Err()
					global.GVA_LOG.Info("异步重新生成平台维度缓存成功", zap.String("platform", pf))
				} else {
					global.GVA_LOG.Warn("异步生成平台维度缓存失败", zap.String("platform", pf), zap.Error(err))
				}
			}
		}
	}()
}
